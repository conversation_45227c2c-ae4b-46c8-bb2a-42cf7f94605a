import React, { useState } from 'react';
import { toast } from 'react-toastify';
import { creditCustomer } from '../../lib/wallet';
import FindCustomerWidget from './findCustomerWidget';

function CreditCustomer() {
  const [isLoading, setIsLoading] = useState(false);
  const [creditDetails, setCreditDetails] = useState({
    amount: '',
    customerId: '',
  });

  const [customerData, setCustomerData] = useState(null);

  const handleInput = (e) => {
    const { name, value } = e.target;
    setCreditDetails((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!creditDetails.customerId) {
      toast.error("No customer selected!");
      return;
    }
    setIsLoading(true);
    try {
      const response = await creditCustomer({
        amount: Number(creditDetails.amount),
        customerId: creditDetails.customerId,
      });
      if (response) {
        toast.success("Customer credited successfully!");

      }
    } catch (error) {
      console.error(error);
      toast.error("Transaction failed!");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white relative rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold mb-4">Credit Customer</h2>


      <FindCustomerWidget
        label="Customer Email"
        onCustomerFound={(data) => {

          const customer = data?.user?.virtualAccount;
          if (customer && customer.customer_id) {
            setCreditDetails((prev) => ({
              ...prev,
              customerId: customer.customer_id,
            }));
            setCustomerData(data);
          }
        }}
      />

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Amount</label>
          <input
            type="number"
            name="amount"
            value={creditDetails.amount}
            onChange={handleInput}
            step="0.01"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>

        <button
          type="submit"
          disabled={isLoading || !creditDetails.customerId || !creditDetails.amount}
          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          {isLoading ? 'Processing...' : 'Credit Customer'}
        </button>
      </form>
    </div>
  );
}

export default CreditCustomer;
