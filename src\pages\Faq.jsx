import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { getAuthToken } from "../lib/auth";


export function FAQ() {
  const [features, setFeatures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedFeature, setSelectedFeature] = useState(null);
  const [isSlideSheetOpen, setIsSlideSheetOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isEditing, setIsEditing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false); 
  const [deletingId, setDeletingId] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    fetchFeatures();
  }, [currentPage]);

  const fetchFeatures = async () => {
    const token = getAuthToken();
    if (!token) {
      toast.error("Please login to access FAQs");
      navigate("/login");
      return;
    }

    try {
      setLoading(true);
      const response = await axios.get(
        `https://api.jaraepay.us/api/v1/admin/chatbot/all-features?page=${currentPage}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data) {
        setFeatures(response.data.data || []);
        setTotalPages(response.data.totalPages || 1);
      }
    } catch (error) {
      if (error.response?.status === 401) {
        toast.error("Session expired. Please login again.");
        navigate("/login");
      } else {
        toast.error("Failed to fetch features");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleView = (feature) => {
    navigate(`/faq/${feature._id}`);
  };

  const handleEdit = (feature) => {
    setSelectedFeature(feature);
    setIsSlideSheetOpen(true);
    setIsEditing(true);
  };

  const handleDelete = async (featureId) => {
    if (!window.confirm("Are you sure you want to delete this feature?"))
      return;

    const token = getAuthToken();
    if (!token) {
      toast.error("Please login to delete features");
      navigate("/login");
      return;
    }


    const previous = features;
    setFeatures((curr) => curr.filter((f) => f._id !== featureId));
    setDeletingId(featureId);

    try {
      await axios.delete(
        `https://api.jaraepay.us/api/v1/admin/chatbot/feature-qa/${featureId}`,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      toast.success("Feature deleted successfully");
    } catch (error) {
      setFeatures(previous);
      if (error?.response?.status === 401) {
        toast.error("Session expired. Please login again.");
        navigate("/login");
      } else {
        toast.error(
          error.response?.data?.message || "Failed to delete feature"
        );
      }
    } finally {
      setDeletingId(null);
    }
  };

  const handleUpdate = async (updatedFeature) => {
    if (!updatedFeature.name.trim() || !updatedFeature.description.trim()) {
      toast.error("Please fill in all fields");
      return;
    }

    const isDuplicate = features.some(
      (feature) =>
        feature.name.toLowerCase() === updatedFeature.name.toLowerCase() &&
        feature._id !== updatedFeature._id
    );

    if (isDuplicate) {
      toast.error("A feature with this name already exists");
      return;
    }

    const token = getAuthToken();
    setIsUpdating(true);

    try {
      const response = await axios.put(
        `https://api.jaraepay.us/api/v1/admin/chatbot/support-feature/${updatedFeature._id}`,
        {
          name: updatedFeature.name,
          description: updatedFeature.description,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.data?.message === "Feature updated successfully") {
        toast.success("Feature updated successfully");
        setIsSlideSheetOpen(false);
        fetchFeatures(); 
      }
    } catch (error) {
      console.error("Update error:", error);
      if (error.response) {
        if (error.response.status === 401) {
          toast.error("Session expired. Please login again.");
          navigate("/login");
        } else if (error.response.status === 409) {
          toast.error("A feature with this name already exists");
        } else {
          toast.error(
            error.response.data?.message || "Failed to update feature"
          );
        }
      } else {
        toast.error("Network error. Please try again.");
      }
    } finally {
      setIsUpdating(false);
    }
  };

  const renderFeaturesTable = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      );
    }

    if (features.length === 0) {
      return <div className="text-center py-10">No features found</div>;
    }

    return (
      <table className="w-full bg-white border-collapse border border-gray-200">
        <thead className="bg-gray-100">
          <tr>
            <th className="border p-2 text-left">Actions</th>
            <th className="border p-2 text-left">Feature Name</th>
          </tr>
        </thead>
        <tbody>
          {features.map((feature) => (
            <tr key={feature._id} className="hover:bg-gray-100">
              <td className="border p-2 space-x-2">
                <button
                  onClick={() => handleView(feature)}
                  className="text-blue-500 bg-slate-50 hover:underline"
                >
                  View
                </button>
                <button
                  onClick={() => handleEdit(feature)}
                  className="text-green-500 bg-slate-50 hover:underline"
                >
                  Edit
                </button>
                <button
                  onClick={() => handleDelete(feature._id)}
                  className="text-purple-500 bg-slate-50 hover:underline"
                  disabled={deletingId === feature._id}
                >
                  Delete
                </button>
              </td>
              <td className="border p-2">{feature.name}</td>
            </tr>
          ))}
        </tbody>
      </table>
    );
  };

  const renderSlideSheet = () => {
    if (!selectedFeature) return null;

    return (
      <div
        className={`fixed inset-y-0 right-0 w-96 bg-white shadow-lg transform transition-transform duration-300 ease-in-out ${
          isSlideSheetOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="p-6">
          <button
            onClick={() => {
              setIsSlideSheetOpen(false);
              setSelectedFeature(null);
            }}
            className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>

          {!isEditing ? (
            <>
              <h2 className="text-xl font-bold mb-4">Feature Details</h2>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium text-gray-700">Name:</h3>
                  <p className="mt-1">{selectedFeature.name}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-700">Description:</h3>
                  <p className="mt-1">{selectedFeature.description}</p>
                </div>
              </div>
            </>
          ) : (
            <>
              <h2 className="text-xl font-bold mb-4">Edit Feature</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    value={selectedFeature.name}
                    onChange={(e) =>
                      setSelectedFeature({
                        ...selectedFeature,
                        name: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    disabled={isUpdating}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={selectedFeature.description}
                    onChange={(e) =>
                      setSelectedFeature({
                        ...selectedFeature,
                        description: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    rows="5"
                    disabled={isUpdating}
                  />
                </div>
                <button
                  onClick={() => handleUpdate(selectedFeature)}
                  className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 ${
                    isUpdating ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Updating...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    );
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    return (
      <div className="flex justify-center mt-6">
        <nav className="inline-flex rounded-md shadow">
          <button
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="px-3 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>

          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => setCurrentPage(page)}
              className={`px-3 py-2 border-t border-b border-gray-300 bg-white text-sm font-medium ${
                currentPage === page
                  ? "bg-blue-50 text-blue-600 border-blue-500"
                  : "text-gray-700 hover:bg-gray-50"
              }`}
            >
              {page}
            </button>
          ))}

          <button
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
            className="px-3 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </nav>
      </div>
    );
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Support Features</h1>
        <button
          onClick={() => navigate("/create_faq")}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
        >
          Add New Feature
        </button>
      </div>
      {renderFeaturesTable()}
      {renderPagination()} 
      {renderSlideSheet()}
    </div>
  );
}
