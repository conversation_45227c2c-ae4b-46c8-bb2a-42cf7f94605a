import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { listUsers, getUserDetails, exportAdminUsersCsv, updateAdminUser } from '../lib/user';

export function Users() {

  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [editUser, setEditUser] = useState(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(true);
  const [isSlideSheetOpen, setIsSlideSheetOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const navigate = useNavigate();

  const handleViewTransactions = (user) => {
    navigate("/transactions", { state: { email: user.email } });
  };
  useEffect(() => {
    fetchUsers();
  }, [page]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await listUsers(page);
      if (response.valid) {
        setUsers(response.data);
        setTotalPages(response.totalPages);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUserSelect = async (userId) => {
    try {
      const response = await getUserDetails(userId);
      if (response.valid) {
        setSelectedUser(response.data);
        setIsSlideSheetOpen(true);
        setIsEditing(false);
      }
    } catch (error) {
      console.error('Error fetching user details:', error);
    }
  };
  const handleEditUser = (user) => {

    setEditUser({ ...user });
    setIsEditing(true);
    setIsSlideSheetOpen(true);
  };

  const handleUpdateUser = async (e) => {
    e.preventDefault();
    try {
      const response = await updateAdminUser(editUser._id, {
        name: editUser.name,
        // Add other fields you want to update
      });

      if (response.valid) {
        toast.success("Update successful")
        // Update local state
        setUsers(users.map(user =>
          user._id === editUser._id ? { ...user, name: editUser.name } : user
        ));

        // Reset edit state
        setIsEditing(false);
        setEditUser(null);
        setIsSlideSheetOpen(false);

        // Optional: Show success message
      }
    } catch (error) {
      console.error('Error updating user:', error);
      // Optional: Show error message
    }
  };

  const handleExportCsv = async () => {
    try {
      const response = await exportAdminUsersCsv();
      if (response) {
        // Create a blob URL and trigger download
        const url = window.URL.createObjectURL(new Blob([response]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'users.csv');
        document.body.appendChild(link);
        link.click();
        link.remove();
      }
    } catch (error) {
      console.error('Error exporting CSV:', error);
      // Optional: Add user-friendly error notification
    }
  };

  // const renderUserTable = () => {
  //   return (
  //     <table className="w-full bg-white border-collapse border border-gray-200">
  //       <thead className="bg-gray-100">
  //         <tr>
  //           <th className="border p-2 text-left">Actions</th>
  //           <th className="border p-2 text-left">Name</th>
  //           <th className="border p-2 text-left">Email</th>
  //           <th className="border p-2 text-left">Role</th>
  //         </tr>
  //       </thead>
  //       <tbody>
  //         {users.map((user) => (
  //           <tr
  //             key={user._id}
  //             className="hover:bg-gray-100"
  //           >
  //             <td className="border p-2 space-x-2">
  //               <button
  //                 onClick={() => handleUserSelect(user._id)}
  //                 className="text-blue-500 bg-slate-50 hover:underline"
  //               >
  //                 View
  //               </button>
  //               <button
  //                 onClick={() => handleEditUser(user)}
  //                 className="text-green-500 bg-slate-50 hover:underline"
  //               >
  //                 Edit
  //               </button>
  //             </td>
  //             <td className="border p-2">{user.name}</td>
  //             <td className="border p-2">{user.email}</td>
  //             <td className="border p-2">{user.role}</td>
  //           </tr>
  //         ))}
  //       </tbody>
  //     </table>
  //   );
  // };

  const renderUserTable = () => {
    return (
      <table className="w-full bg-white border-collapse border border-gray-200">
        <thead className="bg-gray-100">
          <tr>
            <th className="border p-2 text-left">Actions</th>
            <th className="border p-2 text-left">Name</th>
            <th className="border p-2 text-left">Email</th>
            <th className="border p-2 text-left">Role</th>
          </tr>
        </thead>
        <tbody>
          {users.map((user) => (
            <tr key={user._id} className="hover:bg-gray-100">
              <td className="border p-2 space-x-2">
                <button
                  onClick={() => handleUserSelect(user._id)}
                  className="text-blue-500 bg-slate-50 hover:underline"
                >
                  View
                </button>
                <button
                  onClick={() => handleEditUser(user)}
                  className="text-green-500 bg-slate-50 hover:underline"
                >
                  Edit
                </button>
                <button
                  onClick={() => handleViewTransactions(user)}
                  className="text-purple-500 bg-slate-50 hover:underline"
                >
                  View Transactions
                </button>
              </td>
              <td className="border p-2">{user.name}</td>
              <td className="border p-2">{user.email}</td>
              <td className="border p-2">{user.role}</td>
            </tr>
          ))}
        </tbody>
      </table>
    );
  };

  const renderSlideSheet = () => {
    if (!selectedUser && !editUser) return null;

    const currentUser = isEditing ? editUser : selectedUser;

    return (
      <div
        className={`fixed inset-y-0 right-0 w-96 bg-white shadow-lg transform transition-transform duration-300 ease-in-out 
          ${isSlideSheetOpen ? 'translate-x-0' : 'translate-x-full'}`}
      >
        <div className="p-6">
          <button
            onClick={() => {
              setIsSlideSheetOpen(false);
              setIsEditing(false);
              setEditUser(null);
            }}
            className="absolute top-4 right-4 text-gray-200 hover:text-gray-900"
          >
            ✕
          </button>

          {!isEditing ? (
            <>
              <h2 className="text-xl font-bold mb-4">{currentUser.name}</h2>
              <div className="space-y-3">
                <p><strong>Email:</strong> {currentUser.email}</p>
                <p><strong>Phone:</strong> {currentUser.phone}</p>
                <p><strong>Business:</strong> {currentUser.businessname}</p>
                <p><strong>Role:</strong> {currentUser.role}</p>
              </div>
            </>
          ) : (
            <form onSubmit={handleUpdateUser} className="space-y-4">
              <h2 className="text-xl font-bold mb-4">Edit User</h2>
              <div>
                <label className="block mb-2">Name</label>
                <input
                  type="text"
                  value={editUser.name}
                  onChange={(e) => setEditUser({ ...editUser, name: e.target.value })}
                  className="w-full p-2 border rounded"
                  required
                />
              </div>
              {/* Add more editable fields as needed */}
              <div className="flex space-x-2">
                <button
                  type="submit"
                  className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
                >
                  Save Changes
                </button>
                <button
                  type="button"
                  onClick={() => setIsEditing(false)}
                  className="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300"
                >
                  Cancel
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    );
  };

  const renderPagination = () => {
    return (
      <div className="flex justify-between mt-4">
        <button
          onClick={() => setPage(page - 1)}
          disabled={page === 1}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          Previous
        </button>
        <span>Page {page} of {totalPages}</span>
        <button
          onClick={() => setPage(page + 1)}
          disabled={page === totalPages}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          Next
        </button>
      </div>
    );
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Users</h1>
        <button
          onClick={handleExportCsv}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition"
        >
          Export CSV
        </button>
      </div>
      {loading ? (
        <div>Loading...</div>
      ) : (
        <>
          {renderUserTable()}
          {renderPagination()}
        </>
      )}
      {renderSlideSheet()}
    </div>
  );
}