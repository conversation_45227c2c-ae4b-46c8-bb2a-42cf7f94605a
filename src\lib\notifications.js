import { fetchApi } from './api';

export async function getNotifications(params = {}) {
  const queryString = new URLSearchParams(params).toString();
  return fetchApi(`/notifications?${queryString}`);
}


export async function createNotificationByRole(data) {
  return fetchApi('/api/v1/admin/notifications/batch/sms/by-role', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

export async function createNotificationByID(data) {
  return fetchApi('/api/v1/admin/notifications/batch/sms/by-ids', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}
export async function createEmailNotificationByRole(data) {
  return fetchApi('/api/v1/admin/notifications/batch/email/by-role', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}
export async function createEmailNotificationByID(data) {
  return fetchApi('//api/v1/admin/notifications/batch/email/by-ids', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}
export async function createAppNotificationByRole(data) {
  return fetchApi('/api/v1/admin/notifications/batch/push/all', {
    method: 'POST',
    body: JSON.stringify(data),
  });
 
}
 export async function createAppNotificationByID(data) {
  return fetchApi('/api/v1/admin/notifications/batch/push/by-ids', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}



export async function updateNotification(id, data) {
  return fetchApi(`/notifications/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

export async function deleteNotification(id) {
  return fetchApi(`/notifications/${id}`, {
    method: 'DELETE',
  });
}
