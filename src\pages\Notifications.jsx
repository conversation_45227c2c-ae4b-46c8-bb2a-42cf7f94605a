import { useState } from "react";
import { NotificationList } from "../components/notifications/NotificationList";

export function Notifications() {
  const [activeTab, setActiveTab] = useState("SMS Notification");

  const tabs = ["SMS Notification", "Email Notification", "App Notification"];

  return (
    <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div className="px-4 sm:px-0">
        <h1 className="text-2xl font-semibold text-gray-900">Notifications</h1>
      </div>


      <div className="mt-6 flex border-b">
        {tabs.map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 text-sm font-medium ${activeTab === tab
              ? "border-b-2 border-indigo-600 text-white dark:bg-indigo-600 dark:text-white bg-indigo-600"
              : "text-gray-500 dark:bg-[#f9f9f9] dark:text-gray-500 hover:text-indigo-500"
              }`}
          >
            {tab}
          </button>
        ))}
      </div>


      <div className="mt-8 bg-white shadow rounded-lg p-6">
        <NotificationList activeTab={activeTab} />
      </div>
    </div>
  );
}
