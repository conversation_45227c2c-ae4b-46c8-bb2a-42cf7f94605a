import { fetchApi } from './api';

export async function getLogs(params = {}) {
  const queryString = new URLSearchParams(params).toString();
  const url = `/api/v1/admin/logs?${queryString}`;
  console.log("Request URL:", url); // Log the full URL
  return fetchApi(url);
}


export async function createLog(data) {
  return fetchApi('/logs', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

export async function updateLog(id, data) {
  return fetchApi(`/api/v1/admin/logs/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

export async function deleteLog(id) {
  return fetchApi(`/api/v1/admin/logs/${id}`, {
    method: 'DELETE',
  });
}
export async function deleteBatchLog(formInfo) {
  return fetchApi(`/api/v1/admin/logs/batch`, {
    method: 'DELETE',
     body: JSON.stringify(formInfo),
  });
}
