import React, { useState, useEffect } from "react";
import { useFetchMerchant } from "../../query/webhook";
import { useNavigate } from "react-router-dom";
import { FaEye } from 'react-icons/fa'

function MerchantList() {

  const [searchTerm, setSearchTerm] = useState({
    search: "",
    page: 1,

  });
  const { data: fetchMerchant } = useFetchMerchant(searchTerm);
  const Navigate = useNavigate()

  useEffect(() => {
    setSearchTerm(prev => ({ ...prev, page: 1 }));
  }, [setSearchTerm.search]);

  const handlePageChange = (direction) => {
    setSearchTerm(prev => ({ ...prev, page: prev.page + direction }));
  };



  const handleViewMerchant = (id) => {
    Navigate(`/view_webhook/${id}`)
  }
  const handleSearch = (e) => {
    const { name, value } = e.target
    setSearchTerm((prev) => ({
      ...prev,
      [name]: value
    }))
  }
  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Merchant List</h2>
      <input
        type="text"
        placeholder="Search merchants..."
        value={searchTerm.search}
        name="search"
        onChange={handleSearch}
        className="mb-4 w-full p-2 border border-gray-300 rounded-md shadow-sm"
      />
      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm">
          <thead className="bg-gray-100 border-b">
            <tr>
              <th className="py-2 px-4 text-left">Business Name</th>
              <th className="py-2 px-4 text-left">Name</th>
              <th className="py-2 px-4 text-left">Email</th>
              <th className="py-2 px-4 text-left">Phone</th>
              <th className="py-2 px-4 text-left">Country</th>
              <th className="py-2 px-4 text-left">Created At</th>
              <th className="py-2 px-4 text-left">Action</th>
            </tr>
          </thead>
          <tbody>
            {fetchMerchant?.data?.length > 0 ? (
              fetchMerchant?.data?.map((merchant) => (
                <tr key={merchant._id} className="border-b hover:bg-gray-50">
                  <td className="py-2 px-4">{merchant.businessname}</td>
                  <td className="py-2 px-4">{merchant.name}</td>
                  <td className="py-2 px-4">{merchant.email}</td>
                  <td className="py-2 px-4">{merchant.phone}</td>
                  <td className="py-2 px-4">{merchant.country}</td>
                  <td className="py-2 px-4">{new Date(merchant.createdAt).toLocaleDateString()}</td>
                  <td className="py-2 px-4 relative">
                    <button
                      className="w-10 h-10 bg-gray-100 border border-gray-300 rounded-lg flex justify-center items-center hover:bg-gray-200 focus:outline-none"
                      aria-label="View"
                      onClick={() => handleViewMerchant(merchant._id)}
                    >
                      <div>
                        <FaEye size={25} color="green" />
                      </div>
                    </button>
                  </td>

                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="text-center py-4">
                  No merchants found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <div className="flex justify-between items-center mt-4">
        <button
          onClick={() => handlePageChange(-1)}
          disabled={searchTerm.page === 1}
          className="px-4 py-2 border rounded-md text-gray-700 disabled:opacity-50"
        >
          Previous
        </button>
        <span className="text-sm text-gray-500">Page {searchTerm.page}</span>
        <button
          onClick={() => handlePageChange(1)}
          disabled={searchTerm.page === fetchMerchant?.totalPages || searchTerm.page > searchTerm?.totalPages}
          className="px-4 py-2 border rounded-md text-gray-700 disabled:opacity-50"
        >
          Next
        </button>
      </div>
    </div>
  );
}

export default MerchantList;
