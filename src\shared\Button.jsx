import PropTypes from "prop-types";

const Button = ({ label, onClick, variant = "primary", size = "md", disabled = false, icon: Icon }) => {
  const baseStyles = "flex items-center justify-center font-semibold rounded-lg transition duration-200";
  const sizeStyles = {
    sm: "px-3 py-1 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-5 py-3 text-lg",
  };
  const variantStyles = {
    primary: "bg-blue-600 text-white hover:bg-blue-700",
    secondary: "bg-gray-600 text-white hover:bg-gray-700",
    danger: "bg-red-600 text-white hover:bg-red-700",
    outline: "border border-gray-400 text-gray-700 hover:bg-gray-100",
    success: "border border-green-400 bg-green-700 text-white hover:bg-green-900",
    indigo: "border border-indigo-400 bg-indigo-700 text-white hover:bg-indigo-900"
  };

  return (
    <button
      className={`${baseStyles} ${sizeStyles[size]} ${variantStyles[variant]} ${disabled ? "opacity-50 cursor-not-allowed" : ""}`}
      onClick={onClick}
      disabled={disabled}
    >
      {Icon && <Icon className="mr-2 text-lg" />}
      {label}
    </button>
  );
};

Button.propTypes = {
  label: PropTypes.string.isRequired,
  onClick: PropTypes.func,
  variant: PropTypes.oneOf(["primary", "secondary", "danger", "outline"]),
  size: PropTypes.oneOf(["sm", "md", "lg"]),
  disabled: PropTypes.bool,
  icon: PropTypes.elementType,
};

export default Button;
