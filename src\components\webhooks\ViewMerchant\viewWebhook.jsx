import React from 'react'
import { useEffect } from 'react'
import Swal from 'sweetalert2'
import { useParams } from 'react-router-dom'
import { useState } from 'react'
import { FaEye, FaRedo } from 'react-icons/fa'
import { toast } from 'react-toastify'
import { Tooltip } from "react-tooltip";
import WebhookDetailsModal from './webModalDetails'
import { useQueryClient } from "@tanstack/react-query";
import { useFetchWebhookEvent } from '../../../query/webhook'
import { retryWebhookEvent } from '../../../lib/webhooks'

function ViewWebhookEvent() {
  const { id } = useParams()
  const [isModalOpen, setModalOpen] = useState(false);
  const queryClient = useQueryClient();
  const [selectedWebhook, setSelectedWebhook] = useState(null);

  const [searchTerm, setSearchTerm] = useState({
    search: "",
    page: 1,

  });
  const { data: fetchWebhookEvent } = useFetchWebhookEvent(searchTerm, id)
  console.log(fetchWebhookEvent);
  useEffect(() => {
    setSearchTerm(prev => ({ ...prev, page: 1 }));
  }, [setSearchTerm.search]);

  const handlePageChange = (direction) => {
    setSearchTerm(prev => ({ ...prev, page: prev.page + direction }));
  };

  const handleSearch = (e) => {
    const { name, value } = e.target
    setSearchTerm((prev) => ({
      ...prev,
      [name]: value
    }))
  }
  const openModal = (webhook) => {
    setSelectedWebhook(webhook);
    setModalOpen(true);
  };
  async function handleRetry(eventId, url, event, status) {
    try {

      const result = await Swal.fire({
        title: "Are you sure?",
        text: "Do you want to retry this event?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes, retry it!",
        cancelButtonText: "Cancel",
      });
      if (!result.isConfirmed) return;


      Swal.fire({
        title: "Processing...",
        text: "Please wait while the event is being retried.",
        allowOutsideClick: false,
        allowEscapeKey: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      // Make the API request
      const response = await retryWebhookEvent(eventId, {
        url,
        status,
        events: event,
      });


      if (response.valid) {
        Swal.fire({
          title: "Retry Successful",
          text: "The webhook event was retried successfully.",
          icon: "success",
        });
      } else {

        const errorMessage = `${response.message || ""}${response.message && response.error ? " - " : ""}${response.error || ""}`.trim();
        Swal.fire({
          title: "Retry Failed",
          html: `
          <strong>Error:</strong> ${errorMessage}<br/>
          <pre style="text-align:left;white-space:pre-wrap;
                     background:#ffeeba;padding:10px;border-radius:5px;
                     color:#856404;">
${JSON.stringify(response, null, 2)}
          </pre>
        `,
          icon: "error",
          width: 600,
        });
      }
    } catch (error) {
      console.error("Retry error:", error);
      // If an exception is thrown (e.g., network error or Axios error), try to extract its details.
      const serverResponse = error.response?.data || {};
      const errorMessage =
        error.response?.data?.error || error.message || serverResponse.message || "Unknown error occurred";
      Swal.fire({
        title: "Error!",
        html: `
        <strong>Error Message:</strong> ${errorMessage}<br/>
       
       
      `,
        icon: "error",
        width: 600,
      });
    }
  }








  return (
    <div className="p-4">
      <div className='flex justify-between'>
        <h2 className="text-xl font-bold mb-4">WebhookEvent List</h2>

      </div>

      <input
        type="text"
        placeholder="Search merchants..."
        value={searchTerm.search}
        name="search"
        onChange={handleSearch}
        className="mb-4 w-full  p-2 border border-gray-300 rounded-md shadow-sm"
      />
      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm">
          <thead className="bg-gray-100 border-b">
            <tr>
              <th className="py-2 px-4 text-left">Id</th>

              <th className="py-2 px-4 text-left">Event</th>
              <th className="py-2 px-4 text-left">Created At</th>

              <th className="py-2 px-4 text-left">Status</th>
              <th className="py-2 px-4 text-left">Action</th>

            </tr>
          </thead>
          <tbody>
            {fetchWebhookEvent?.data?.length > 0 ? (
              fetchWebhookEvent?.data?.map((merchant) => (
                <tr key={merchant._id} className="border-b hover:bg-gray-50">
                  <td className="py-2 px-4">{merchant._id}</td>
                  <td className="py-2 px-4">{merchant.event}</td>
                  <td className="py-2 px-4">{new Date(merchant.created_at).toLocaleDateString()}</td>


                  <td className="py-2 px-4 flex items-center space-x-2">

                    <span
                      className={`px-3 py-1 text-xs font-medium rounded-full w-fit min-w-[90px] text-center
              ${merchant?.payload?.transaction?.status === "failed" ? "bg-red-100 text-red-600" :
                          merchant?.payload?.transaction?.status === "initiated" ? "bg-yellow-100 text-yellow-600" :
                            merchant?.payload?.transaction?.status === "completed" ? "bg-green-100 text-green-600" :
                              "bg-blue-100 text-blue-600"}
            `}
                    >
                      {merchant?.payload?.transaction?.status || "Unknown"}
                    </span>


                    <span
                      className={`px-3 py-1 text-xs font-medium rounded-full w-fit min-w-[90px] text-center
              ${merchant?.status === "failed" ? "bg-red-100 text-red-600" :
                          merchant?.status === "success" ? "bg-green-100 text-green-600" :
                            "bg-blue-100 text-blue-600"}
            `}
                    >
                      {merchant?.status || "Unknown"}
                    </span>


                    <button
                      onClick={() => handleRetry(merchant._id, merchant.webhook_id.url, merchant.event, merchant.webhook_id.status)}
                      className="flex items-center justify-center p-2 bg-yellow-500 text-white rounded-full hover:bg-yellow-600 transition duration-200 shadow-sm focus:outline-none"
                      aria-label="Retry"
                      data-tooltip-id="retry-tooltip"
                      data-tooltip-content="Retry Webhook"
                    >
                      <FaRedo size={14} />
                    </button>

                  </td>


                  <td className="py-2 px-4">
                    <button
                      className="p-2 bg-green-600 text-white rounded-lg hover:bg-green-900 transition duration-200"
                      aria-label="View"
                      onClick={() => openModal(merchant)}
                    >
                      <FaEye size={16} />
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="text-center py-4">
                  No webhook events found
                </td>
              </tr>
            )}
          </tbody>


        </table>
      </div>
      <div className="flex justify-between items-center mt-4">
        <button
          onClick={() => handlePageChange(-1)}
          disabled={searchTerm.page === 1}
          className="px-4 py-2 border rounded-md text-gray-700 disabled:opacity-50"
        >
          Previous
        </button>
        <span className="text-sm text-gray-500">Page {searchTerm.page}</span>
        <button
          onClick={() => handlePageChange(1)}
          disabled={searchTerm.page === fetchWebhookEvent?.totalPages || searchTerm.page > fetchWebhookEvent?.totalPages}
          className="px-4 py-2 border rounded-md text-gray-700 disabled:opacity-50"
        >
          Next
        </button>
      </div>
      <WebhookDetailsModal isOpen={isModalOpen} onClose={() => setModalOpen(false)} webhookData={selectedWebhook} />
    </div>
  )
}

export default ViewWebhookEvent
