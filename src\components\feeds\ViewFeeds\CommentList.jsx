import React, { useState } from 'react';
import { FaUserCircle } from 'react-icons/fa';
import { BsThreeDotsVertical } from 'react-icons/bs';
import Comment from './Comment';

const CommentList = ({ comments, feedId, handleDeleteComment, handleAddReply, handleDeleteReply }) => {
  const [showMenu, setShowMenu] = useState(null);

  return (
    <div className="mt-6">
      <h3 className="text-lg font-semibold mb-4">Comments</h3>
      <div className="max-h-60 overflow-y-auto">
        {comments?.length > 0 ? (
          <div className="space-y-4 pr-2">
            {comments.map((comment) => (
              <Comment
                key={comment._id}
                comment={comment}
                feedId={feedId}
                showMenu={showMenu}
                setShowMenu={setShowMenu}
                handleDeleteComment={handleDeleteComment}
                handleDeleteReply={handleDeleteComment}
                handleAddReply={handleAddReply}
              />
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-center">No comments yet. Be the first to comment!</p>
        )}
      </div>
    </div>
  );
};

export default CommentList;


