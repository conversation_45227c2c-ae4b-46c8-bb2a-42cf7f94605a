import React from 'react'
import { useState } from 'react';
import Modal from '../../../shared/Modal';
import { useParams } from 'react-router-dom'

import { FaTrashAlt, FaBan } from 'react-icons/fa';

import { useEffect } from 'react'
import { getTeamMembers, removeTeamMember } from '../../../lib/team'
import { useFetchTeamMembers } from '../../../query/team'
import { toast } from 'react-toastify';

function MemberDisplay() {
  const { id } = useParams()
  console.log(id)
  const { data: teamMembers, isPending, isError } = useFetchTeamMembers(id)
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [userId, setUserId] = useState("")
  const [teamId, setTeamId] = useState('')



  useEffect(() => {
    const fetchMembers = async () => {
      try {
        const response = await getTeamMembers(id)
        console.log(response)
        toast.success(response.message)
      } catch (error) {
        console.log(error)
      }
    }
    fetchMembers()
  }, [])


  const handleOpenModal = (id, teamID) => {
    setUserId(id)
    setTeamId(teamID)
    setIsModalOpen(true);
  };


  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleConfirmDelete = async () => {
    try {
      const response = await removeTeamMember(userId, teamId);

      if (response?.status === 200 || response?.status === 204) {
        setIsModalOpen(false);
        toast.success("Member deleted successfully");
      } else {
        throw new Error("Failed to delete member. Please try again.");
      }
    } catch (error) {
      console.error("Error deleting member:", error);


      const errorMessage =
        error?.response?.message ||
        error?.message ||
        "An error occurred while deleting the member.";

      if (errorMessage.includes("Cannot remove the team owner")) {
        toast.error("You cannot remove the team owner.");
      } else {
        toast.error(errorMessage);
      }
    }
  };


  return (
    <div className='space-y-4 mt-5'>
      <div className="max-w-7xl mx-auto p-6 bg-white shadow-lg rounded-lg">
        <h2 className="text-xl font-bold text-gray-800 mb-4">Team Members</h2>

        {teamMembers?.data?.length === 0 ? (
          <p className="text-gray-500">No Members available.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full border border-gray-200 rounded-lg">
              <thead>
                <tr className="bg-gray-100">
                  <th className="py-3 px-4 text-left text-gray-700">ID</th>
                  <th className="py-3 px-4 text-left text-gray-700">Name</th>
                  <th className="py-3 px-4 text-left text-gray-700">Email</th>
                  <th className="py-3 px-4 text-left text-gray-700">Role</th>
                  <th className="py-3 px-4 text-left text-gray-700">Business Name</th>
                  <th className="py-3 px-4 text-left text-gray-700">Country</th>
                  <th className="py-3 px-4 text-left text-gray-700">Created At</th>
                  <th className="py-3 px-4 text-left text-gray-700">Action</th>
                </tr>
              </thead>
              <tbody>
                {teamMembers?.data?.map((member) => {
                  const { teamId, _id, name, email, role, userId, country, createdAt } = member;

                  return (
                    <tr key={_id} className="border-t hover:bg-gray-50 transition">
                      <td className="py-3 px-4">{teamId}</td>
                      <td className="py-3 px-4 ">{userId?.name}</td>
                      <td className="py-3 px-4">{userId?.email}</td>
                      <td className="py-3 px-4 capitalize">{userId?.role}</td>
                      <td className="py-3 px-4">{userId?.businessname || "N/A"}</td>
                      <td className="py-3 px-4">{userId?.country || "N/A"}</td>
                      <td className="py-3 px-4 text-gray-500">
                        {new Date(createdAt).toLocaleDateString()}
                      </td>
                      <td className="py-3 px-4 dark-bg-gray-100 flex space-x-2">
                        <button
                          className="text-red-500 hover:text-red-700"
                          onClick={() => handleOpenModal(teamId, userId?._id,)}
                        >
                          <FaTrashAlt size={20} color="red" />
                        </button>

                      </td>
                    </tr>

                  );
                })}
              </tbody>
            </table>
          </div>
        )}
        <Modal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          title="Confirm Action"
          onConfirm={handleConfirmDelete}
        >
          <p className="text-gray-700">Are you sure you want to proceed?</p>
        </Modal>
      </div>

    </div>


  )
}

export default MemberDisplay
