export async function createFaq(faqData) {
  const response = await fetch("/api/faqs", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(faqData),
  });
  return await response.json();
}

export async function updateFaq(id, faqData) {
  const response = await fetch(`/api/faqs/${id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(faqData),
  });
  return await response.json();
}

export async function fetchFaqs() {
  const response = await fetch("/api/faqs");
  return await response.json();
}

export async function fetchSingleFaq(id) {
  const response = await fetch(`/api/faqs/${id}`);
  return await response.json();
}
