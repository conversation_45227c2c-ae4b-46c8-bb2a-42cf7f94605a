import React, { useState, useEffect } from 'react';
import { createFeed, updateFeed } from '../../../lib/feeds';
import { toast } from 'react-toastify';
import { useFetchFeeds, useFetchSingleFeed } from '../../../query/feeds';
import { useParams } from 'react-router-dom';

function CreateFeeds() {
  const { id } = useParams()
  const [post, setPost] = useState({
    content: "",
    name: ""
  });

  const { data: AllFeed } = useFetchFeeds();
  const { data: fetchSingleFeed } = useFetchSingleFeed(id);


  useEffect(() => {
    if (fetchSingleFeed) {
      setPost({
        name: fetchSingleFeed.name || "",
        content: fetchSingleFeed.content || ""
      });
    }
  }, [fetchSingleFeed]);

  const handlePost = (e) => {
    const { name, value } = e.target;
    setPost((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      let response;
      if (fetchSingleFeed) {

        response = await updateFeed(id, post);
        toast.success("Feed updated successfully");
      } else {

        response = await createFeed(post);
        toast.success("Feed created successfully");
      }
      console.log(response);

      setPost({
        content: "",
        name: ""
      });
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-10">
      <div className="max-w-2xl mx-auto bg-white p-8 rounded-xl shadow-lg">
        <h1 className="text-3xl font-semibold text-center text-gray-800 mb-8">
          {fetchSingleFeed ? "Update Post" : "Create New Post"}
        </h1>
        <form onSubmit={handleSubmit}>

          <div className="mb-6">
            <label htmlFor="post-title" className="block text-sm font-medium text-gray-700 mb-2">Post Title</label>
            <input
              id="post-title"
              type="text"
              value={post.name}
              name='name'
              onChange={handlePost}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Enter title..."
              required
            />
          </div>

          <div className="mb-6">
            <label htmlFor="post-content" className="block text-sm font-medium text-gray-700 mb-2">Post Content</label>
            <textarea
              id="post-content"
              value={post.content}
              name='content'
              onChange={handlePost}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Write your content here..."
              rows="6"
              required
            />
          </div>

          <div className="flex justify-center">
            <button
              type="submit"
              className="px-6 py-2 bg-indigo-600 text-white rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              {fetchSingleFeed ? "Update" : "Post"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default CreateFeeds;
