import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getExportTeamMembers, getTeams } from '../../lib/team';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

export function TeamList() {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);
  const [page, setPage] = useState(1);
  const limit = 30;


  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 600);

    return () => clearTimeout(handler);
  }, [searchTerm]);

  const { data: teamsData, isLoading, error } = useQuery({
    queryKey: ['teams', debouncedSearchTerm, page],
    queryFn: () => getTeams({ search: debouncedSearchTerm, page, limit }),
    keepPreviousData: true,
  });

  const navigate = useNavigate();

  if (isLoading) {
    return <div className="text-center py-10 text-gray-600">Loading teams...</div>;
  }

  if (error) {
    return (
      <div className="text-center text-red-500 py-10">
        Error loading teams: {error.message}
      </div>
    );
  }

  return (
    <div className="max-w-5xl mx-auto px-4 py-8">

      <h1 className="text-3xl font-bold text-center mb-6 text-gray-800">
        Team Management
      </h1>


      <div className="flex flex-col sm:flex-row sm:justify-between items-center mb-6">
        <div className="w-full sm:w-1/2 mb-4 sm:mb-0">
          <input
            type="text"
            placeholder="Search teams..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setPage(1);
            }}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
      </div>


      {teamsData?.data?.length === 0 ? (
        <div className="text-center text-gray-600">No teams found.</div>
      ) : (
        <div className="space-y-6">
          {teamsData?.data?.map((team) => (
            <div
              key={team._id}
              className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200"
            >
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold text-gray-800">
                  {team.name}
                </h3>
                <p className="text-sm text-gray-500">
                  {new Date(team.createdAt).toLocaleDateString()}
                </p>
              </div>
              <div className="flex flex-wrap gap-3">
                <button
                  className="bg-indigo-600 hover:bg-indigo-700 text-white px-5 py-2 rounded-lg shadow transition"
                  onClick={() => navigate(`/teams_invites/${team._id}`)}
                >
                  View Invites
                </button>
                <button
                  className="bg-indigo-500 hover:bg-indigo-600 text-white px-5 py-2 rounded-lg shadow transition"
                  onClick={() => navigate(`/teams_members/${team._id}`)}
                >
                  View Members
                </button>
                <button
                  className="bg-green-600 hover:bg-green-700 text-white px-5 py-2 rounded-lg shadow transition"
                  onClick={() => handleExportMember(team._id)}
                >
                  Export Team Members
                </button>
              </div>
            </div>
          ))}
        </div>
      )}


      <div className="flex justify-center items-center space-x-4 mt-8">
        <button
          onClick={() => setPage((old) => Math.max(old - 1, 1))}
          disabled={page === 1}
          className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50 transition"
        >
          Previous
        </button>
        <span className="text-gray-700 font-medium">
          Page {page} of {teamsData?.totalPages || 1}
        </span>
        <button
          onClick={() => setPage((old) => (page < teamsData?.totalPages ? old + 1 : old))}
          disabled={page === teamsData?.totalPages}
          className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50 transition"
        >
          Next
        </button>
      </div>
    </div>
  );
}
