import { useQuery } from "@tanstack/react-query"
import { getTeamMembers, inviteTeamMember } from "../lib/team"
export const useFetchTeamInvites = (id) => {
   
    return useQuery({
        queryFn: () => inviteTeamMember(id),
        queryKey: ["invite", id],
        
        
    })

}
export const useFetchTeamMembers = (id) => {
   
    return useQuery({
        queryFn: () => getTeamMembers(id),
        queryKey: ["invite", id],
        
        
    })

}