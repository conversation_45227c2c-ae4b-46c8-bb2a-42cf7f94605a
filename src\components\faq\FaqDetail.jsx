import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import axios from "axios";
import { toast } from "react-toastify";
import { getAuthToken } from "../../lib/auth";


function FAQDetail() {
  const { featureId } = useParams();
  const navigate = useNavigate();
  const [feature, setFeature] = useState(null);
  const [faqs, setFaqs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [newFaq, setNewFaq] = useState({ question: "", answer: "" });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingFaqId, setEditingFaqId] = useState(null);
  const [editedFaq, setEditedFaq] = useState({ question: "", answer: "" });

  useEffect(() => {
    const fetchData = async () => {
      const token = getAuthToken();
      if (!token) {
        navigate("/login");
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await axios.get(
          `https://api.jaraepay.us/api/v1/admin/chatbot/featureqas/${featureId}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        console.log("API Response:", response.data); 

        if (!response.data) {
          throw new Error("No data received from server");
        }


        setFeature({
          name: response.data.name,
          description: response.data.description,
        });


        setFaqs(response.data.qas || []);
      } catch (err) {
        console.error("Fetch error:", err);

        if (err.response?.status === 404) {
          setError("Feature not found");
          navigate("/faq", { replace: true });
          toast.error("The requested feature doesn't exist");
          return;
        }

        if (err.response?.status === 401) {
          navigate("/login");
          return;
        }

        setError(err.message || "Failed to load data");
        toast.error(
          err.response?.data?.message || "Failed to load feature details"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [featureId, navigate]);

  const handleFaqSubmit = async (e) => {
    e.preventDefault();
    const token = getAuthToken();

    if (!newFaq.question.trim() || !newFaq.answer.trim()) {
      toast.error("Please fill in both question and answer");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await axios.post(
        `https://api.jaraepay.us/api/v1/admin/chatbot/support-qa/${featureId}`,
        {
          question: newFaq.question,
          answer: newFaq.answer,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );


      setFaqs([...faqs, response.data]);


      setNewFaq({ question: "", answer: "" });
      setShowForm(false);

      toast.success("FAQ added successfully!");
    } catch (error) {
      console.error("Error creating FAQ:", error);
      toast.error(error.response?.data?.message || "Failed to add FAQ");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditFaq = (faq) => {
    setEditingFaqId(faq._id);
    setEditedFaq({
      question: faq.question,
      answer: faq.answer,
    });
  };

  const handleUpdateFaq = async () => {
    if (!editedFaq.question.trim() || !editedFaq.answer.trim()) {
      toast.error("Please fill in both question and answer");
      return;
    }

    const token = getAuthToken();
    try {
      await axios.put(
        `https://api.jaraepay.us/api/v1/admin/chatbot/feature/${featureId}/qa/${editingFaqId}`,
        {
          question: editedFaq.question,
          answer: editedFaq.answer,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );


      setFaqs(
        faqs.map((faq) =>
          faq._id === editingFaqId ? { ...faq, ...editedFaq } : faq
        )
      );

      setEditingFaqId(null);
      toast.success("FAQ updated successfully!");
    } catch (error) {
      console.error("Error updating FAQ:", error);
      toast.error(error.response?.data?.message || "Failed to update FAQ");
    }
  };

  const handleDeleteFaq = async (faqId) => {
    if (!window.confirm("Are you sure you want to delete this FAQ?")) return;

    const token = getAuthToken();
    try {
      await axios.delete(
        `https://api.jaraepay.us/api/v1/admin/chatbot/qa/${faqId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );


      setFaqs(faqs.filter((faq) => faq._id !== faqId));
      toast.success("FAQ deleted successfully!");
    } catch (error) {
      console.error("Error deleting FAQ:", error);
      toast.error(error.response?.data?.message || "Failed to delete FAQ");
    }
  };

  const cancelEdit = () => {
    setEditingFaqId(null);
    setEditedFaq({ question: "", answer: "" });
  };



  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-50 border-l-4 border-red-500 p-4">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700">Error: {error}</p>
              <button
                onClick={() => window.location.reload()}
                className="mt-2 text-sm text-red-500 hover:text-red-700"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">
          {feature?.name || "Feature Details"}
        </h1>

        <div className="flex space-x-2">
          <button
            onClick={() => setShowForm(!showForm)}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            {showForm ? "Cancel" : "Add New FAQ"}
          </button>
          <button
            onClick={() => navigate("/faq")}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
          >
            ← Back to Features
          </button>
        </div>
      </div>

      {feature?.description && (
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-2">Description:</h2>
          <p className="text-gray-700">{feature.description}</p>
        </div>
      )}

      {showForm && (
        <div className="bg-white p-6 rounded-lg shadow-md mb-8">
          <h2 className="text-xl font-bold mb-4">Add New FAQ</h2>
          <form onSubmit={handleFaqSubmit}>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Question
              </label>
              <input
                type="text"
                value={newFaq.question}
                onChange={(e) =>
                  setNewFaq({ ...newFaq, question: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Answer
              </label>
              <textarea
                value={newFaq.answer}
                onChange={(e) =>
                  setNewFaq({ ...newFaq, answer: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                rows="4"
                required
              />
            </div>
            <button
              type="submit"
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Submitting..." : "Post FAQ"}
            </button>
          </form>
        </div>
      )}

<div className="mb-6">
        <h2 className="text-xl font-bold mb-4">FAQs</h2>

        {faqs.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No FAQs found for this feature
          </div>
        ) : (
          <div className="space-y-4">
            {faqs.map((faq) => (
              <div key={faq._id} className="bg-white p-4 rounded-lg shadow">
                {editingFaqId === faq._id ? (
                  <div className="space-y-3">
                    <input
                      type="text"
                      value={editedFaq.question}
                      onChange={(e) => setEditedFaq({...editedFaq, question: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                    <textarea
                      value={editedFaq.answer}
                      onChange={(e) => setEditedFaq({...editedFaq, answer: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      rows="3"
                    />
                    <div className="flex space-x-2">
                      <button
                        onClick={handleUpdateFaq}
                        className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
                      >
                        Save
                      </button>
                      <button
                        onClick={cancelEdit}
                        className="px-3 py-1 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold text-lg mb-2">{faq.question}</h3>
                        <p className="text-gray-700">{faq.answer}</p>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEditFaq(faq)}
                          className="text-green-500 bg-slate-50 hover:underline px-2 py-1 rounded"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteFaq(faq._id)}
                          className="text-purple-500 bg-slate-50 hover:underline px-2 py-1 rounded"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default FAQDetail;