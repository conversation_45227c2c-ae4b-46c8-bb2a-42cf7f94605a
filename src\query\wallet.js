import { useQuery } from "@tanstack/react-query"

import { getBanks, getBankValidation } from "../lib/wallet"
export const useFetchBanks = (id) => {
   
    return useQuery({
        queryFn: () => getBanks(),
        queryKey: ["banks"],
        
        
    })

}
export const useFetchBankValidation = (id) => {
   
    return useQuery({
        queryFn: () => getBankValidation(),
        queryKey: ["validation"],
        
        
    })

}

