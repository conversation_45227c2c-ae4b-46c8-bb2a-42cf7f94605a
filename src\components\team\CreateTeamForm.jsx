import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import { createTeam } from "../../lib/team";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const validationSchema = Yup.object({
  email: Yup.string().email("Invalid email address").required("Email is required"),
  role: Yup.string().min(1, "Role is required").required("Role is required"),
});

export function CreateTeamForm() {
  const queryClient = useQueryClient();

  return (
    <Formik
      initialValues={{ email: "", role: "" }}
      validationSchema={validationSchema}
      onSubmit={async (values, { setSubmitting, resetForm }) => {
        try {
          await createTeam(values);

          toast.success("Team successfully created!", { position: "top-right" });
          queryClient.invalidateQueries(["teams"]);
          resetForm();
        } catch (error) {
          toast.error(error.message || "Failed to create team. Please try again.", { position: "top-right" });
          console.error("Failed to create team:", error);
        } finally {
          setSubmitting(false);
        }
      }}
    >

      {({ isSubmitting }) => (
        <Form className="space-y-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <Field
              name="email"
              type="email"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
            <ErrorMessage name="email" component="p" className="mt-1 text-sm text-red-500" />
          </div>

          <div>
            <label htmlFor="role" className="block text-sm font-medium text-gray-700">
              Role
            </label>
            <Field
              name="role"
              type="text"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
            <ErrorMessage name="role" component="p" className="mt-1 text-sm text-red-500" />
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50"
          >
            {isSubmitting ? "Creating..." : "Create Team"}
          </button>
        </Form>
      )}
    </Formik>
  );
}
