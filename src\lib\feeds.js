import { fetchApi } from './api';

// Feeds
export async function getFeeds() {
  // const queryString = new URLSearchParams(params).toString();
  const response = fetchApi('/api/v1/admin/feeds');
  return response
}
export async function getOneFeed(id) {

  const response = fetchApi(`/api/v1/admin/feeds/${id}`);
  return response
}
export async function getOneFeedReply(id) {

  const response = fetchApi(`/api/v1/admin/feeds/${id}/comments`);
  return response
}
export async function getOneFeedComments(id) {

  const response = fetchApi(`/api/v1/admin/feeds/${id}/comments`);
  return response
}


export async function createFeed(data) {
  return fetchApi('/api/v1/admin/feeds', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}
export async function postComment(id, data) {
  return fetchApi(`/api/v1/admin/feeds/${id}/comments`, {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

export async function updateFeed(id, data) {
  return fetchApi(`/api/v1/admin/feeds/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

export async function deleteFeed(id) {
  return fetchApi(`/api/v1/admin/feeds/${id}`, {
    method: 'DELETE',
  });
}

// Feed Comments
export async function getFeedComments(feedId) {
  return fetchApi(`/api/v1/admin/feeds/${feedId}/comments`);
}

export async function createFeedComment(feedId, data) {
  return fetchApi(`/api/v1/admin/feeds/${feedId}/comments`, {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

export async function updateFeedComment(feedId, commentId, data) {
  return fetchApi(`/api/v1/admin/feeds/${feedId}/comments/${commentId}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

export async function deleteFeedComment(feedId, commentId) {
  return fetchApi(`/api/v1/admin/feeds/${feedId}/comments/${commentId}`, {
    method: 'DELETE',
  });
}