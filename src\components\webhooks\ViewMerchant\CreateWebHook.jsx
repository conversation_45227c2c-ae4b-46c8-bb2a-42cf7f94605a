import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";

import { useMutation } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { createWebhook, updateWebhook } from "../../../lib/webhooks";
import { useParams } from "react-router-dom";
import { useFetchSingleWebhook, useFetchWebhook } from "../../../query/webhook";

function CreateWebHook() {
  const { id } = useParams();
  const [searchTerm, setSearchTerm] = useState({
    search: "",
    page: 1,

  });
  const { data: fetchWebhook, isLoading } = useFetchSingleWebhook(id);
  console.log(fetchWebhook)

  const formik = useFormik({
    initialValues: {
      url: "",
      status: "",
    },
    validationSchema: Yup.object({
      url: Yup.string().url("Invalid URL format").required("URL is required"),
      status: Yup.string().oneOf(["active", "inactive"], "Invalid status"),
    }),
    onSubmit: (values, { resetForm }) => {
      if (fetchWebhook) {
        updateMutation.mutate(values);
      } else {
        createMutation.mutate(values, {
          onSuccess: () => resetForm(),
        });
      }
    },
  });

  useEffect(() => {
    if (fetchWebhook) {
      const webhook = fetchWebhook?.webhook;
      formik.setValues({
        url: fetchWebhook?.webhook?.url || "",
        status: webhook.status || "",
      });
    }
  }, [fetchWebhook]);


  const createMutation = useMutation({
    mutationFn: async (values) => createWebhook(id, values),
    onSuccess: () => {
      toast.success("Webhook created successfully!");

    },
    onError: () => {
      toast.error("Failed to create webhook.");
    },
  });


  const updateMutation = useMutation({
    mutationFn: async (values) => updateWebhook(fetchWebhook?.webhook?._id, values),
    onSuccess: () => {
      toast.success("Webhook updated successfully!");
    },
    onError: () => {
      toast.error("Failed to update webhook.");
    },
  });

  return (
    <div className="max-w-lg mx-auto bg-white p-6 rounded-lg shadow-md mt-10">
      <h2 className="text-2xl font-bold text-center mb-4">
        {fetchWebhook ? "Update Webhook" : "Create Webhook"}
      </h2>

      {isLoading ? (
        <p className="text-center">Loading...</p>
      ) : (
        <form onSubmit={formik.handleSubmit}>
          <div className="mb-4">
            <label className="block text-gray-700 font-medium">Webhook URL</label>
            <input
              type="text"
              name="url"
              value={formik.values.url}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              className="w-full border border-gray-300 p-2 rounded-md focus:ring focus:ring-green-300"
              placeholder="Enter webhook URL"
            />
            {formik.touched.url && formik.errors.url && (
              <p className="text-red-500 text-sm mt-1">{formik.errors.url}</p>
            )}
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 font-medium">Status</label>
            <select
              name="status"
              value={formik.values.status}
              onChange={formik.handleChange}
              className="w-full border border-gray-300 p-2 rounded-md focus:ring focus:ring-green-300"
            >
              <option value="">Select Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <button
            type="submit"
            disabled={createMutation.isLoading || updateMutation.isLoading}
            className="w-full bg-green-600 hover:bg-green-700 text-white py-2 rounded-md font-semibold transition"
          >
            {createMutation.isLoading || updateMutation.isLoading
              ? "Submitting..."
              : fetchWebhook
                ? "Update Webhook"
                : "Create Webhook"}
          </button>
        </form>
      )}
    </div>
  );
}

export default CreateWebHook;
