import { fetchApi, fetchApiWithBlob } from './api';

export async function updateUserProfile(data) {
  return fetchApi('/users/profile', {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

export async function updateUserPassword(data) {
  return fetchApi('/users/password', {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

export async function exportUserData() {
  return fetchApiWithBlob('/users/export', {
    method: 'GET',
  });
}

export async function listUsers(page = 1, limit = 30) {
  return fetchApi(`/api/v1/admin/users?page=${page}&limit=${limit}`, {
    method: 'GET',
  });
}

export async function getUserDetails(userId) {
  return fetchApi(`/api/v1/admin/users/${userId}`, {
    method: 'GET',
  });
}

export async function updateAdminUser(userId, data) {
  return fetchApi(`/api/v1/admin/users/${userId}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

export async function exportAdminUsersCsv() {
  return fetchApiWithBlob('/api/v1/admin/users/export/csv', {
    method: 'GET',
  });
}
export async function getTransactions(email) {
  return fetchApi(`/api/v1/admin/wallets/customer/transactions?email=${email}`,{
    method: 'GET',
  });
}