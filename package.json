{"name": "vite-react-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "@hookform/resolvers": "^3.3.4", "@supabase/supabase-js": "^2.39.7", "@tanstack/react-query": "^5.24.1", "autoprefixer": "^10.4.17", "axios": "^1.10.0", "font-awesome": "^4.7.0", "formik": "^2.4.6", "framer-motion": "^12.4.1", "lucide-react": "^0.475.0", "postcss": "^8.4.35", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.50.1", "react-icons": "^5.4.0", "react-query": "^3.39.3", "react-router-dom": "^6.22.2", "react-select": "^5.10.0", "react-toastify": "^11.0.3", "react-tooltip": "^5.28.0", "sweetalert2": "^11.17.2", "tailwindcss": "^3.4.1", "yup": "^1.6.1", "zod": "^3.22.4"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "vite": "^5.4.2"}}