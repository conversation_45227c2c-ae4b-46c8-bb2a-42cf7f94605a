import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { getMerchantDonations, getMerchantDonationSummary, updateDonationStatus } from '../../lib/donations';
import { DonationViewModal } from './DonationViewModal';

export function DonationList() {
  const [donations, setDonations] = useState([]);
  const [summary, setSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedDonation, setSelectedDonation] = useState(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [toggleLoading, setToggleLoading] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    fetchDonations();
    fetchSummary();
  }, [page, statusFilter]);

  const fetchDonations = async () => {
    try {
      setLoading(true);
      const response = await getMerchantDonations(page, 20, statusFilter || null);
      setDonations(response.donations || []);
      setTotalPages(response.totalPages || 0);
    } catch (error) {
      toast.error('Failed to fetch donations');
      console.error('Error fetching donations:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSummary = async () => {
    try {
      const response = await getMerchantDonationSummary();
      setSummary(response);
    } catch (error) {
      console.error('Error fetching summary:', error);
    }
  };

  const handleStatusToggle = async (donationId, currentStatus) => {
    const newStatus = currentStatus === 'active' ? 'stopped' : 'active';
    try {
      setToggleLoading(donationId);
      await updateDonationStatus(donationId, newStatus);
      toast.success(`Donation ${newStatus === 'active' ? 'started' : 'stopped'} successfully`);
      fetchDonations();
      fetchSummary();
    } catch (error) {
      toast.error('Failed to update donation status');
      console.error('Error updating status:', error);
    } finally {
      setToggleLoading(null);
    }
  };

  const handleCreateNew = () => {
    navigate('/donations/create');
  };

  const handleView = (donation) => {
    setSelectedDonation(donation);
    setShowViewModal(true);
  };

  const handleEdit = (donationId) => {
    navigate(`/donations/edit/${donationId}`);
  };

  const handleViewPayments = (donationId) => {
    navigate(`/donations/${donationId}/payments`);
  };

  const closeViewModal = () => {
    setShowViewModal(false);
    setSelectedDonation(null);
  };

  if (loading && page === 1) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm text-blue-600 font-medium">Total Campaigns</p>
            <p className="text-2xl font-bold text-blue-900">{summary.totalCampaigns || 0}</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <p className="text-sm text-green-600 font-medium">Active Campaigns</p>
            <p className="text-2xl font-bold text-green-900">{summary.activeCampaigns || 0}</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <p className="text-sm text-purple-600 font-medium">Total Received</p>
            <p className="text-2xl font-bold text-purple-900">₦{(summary.totalReceived || 0).toLocaleString()}</p>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <p className="text-sm text-orange-600 font-medium">Total Donors</p>
            <p className="text-2xl font-bold text-orange-900">{summary.totalDonors || 0}</p>
          </div>
        </div>
      )}

      {/* Header with Create Button and Filter */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <h2 className="text-lg font-medium text-gray-900">Donation Campaigns</h2>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="stopped">Stopped</option>
          </select>
        </div>
        <button
          onClick={handleCreateNew}
          className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
        >
          Add New Donation
        </button>
      </div>

      {/* Donations Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Subject
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Total Received
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {donations.length === 0 ? (
              <tr>
                <td colSpan="4" className="px-6 py-8 text-center text-gray-500">
                  No donation campaigns found. Create your first campaign to get started.
                </td>
              </tr>
            ) : (
              donations.map((donation) => (
                <tr key={donation._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleView(donation)}
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        View
                      </button>
                      <button
                        onClick={() => handleEdit(donation.donationId)}
                        className="text-gray-600 hover:text-gray-900"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleViewPayments(donation.donationId)}
                        className="text-green-600 hover:text-green-900"
                      >
                        Payments
                      </button>
                      <button
                        onClick={() => handleStatusToggle(donation.donationId, donation.status)}
                        disabled={toggleLoading === donation.donationId}
                        className={`${
                          donation.status === 'active'
                            ? 'text-red-600 hover:text-red-900'
                            : 'text-green-600 hover:text-green-900'
                        } disabled:opacity-50`}
                      >
                        {toggleLoading === donation.donationId ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                        ) : (
                          donation.status === 'active' ? 'Stop' : 'Start'
                        )}
                      </button>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{donation.subject}</div>
                      <div className="text-sm text-gray-500">ID: {donation.donationId}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        donation.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {donation.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">₦{(donation.totalReceived || 0).toLocaleString()}</div>
                      <div className="text-gray-500">{donation.donorCount || 0} donors</div>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Page {page} of {totalPages}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className="px-3 py-2 rounded-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
              className="px-3 py-2 rounded-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* View Modal */}
      {showViewModal && selectedDonation && (
        <DonationViewModal
          donation={selectedDonation}
          onClose={closeViewModal}
        />
      )}
    </div>
  );
}
