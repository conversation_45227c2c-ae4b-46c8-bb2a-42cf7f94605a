// const API_BASE_URL = 'https://api.jaraepay.us';
const API_BASE_URL = 'http://localhost:9000';

export async function fetchApi(endpoint, options = {}) {
  const token = localStorage.getItem('authToken');
  const url = `${API_BASE_URL}${endpoint}`;
  console.log(url);
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers,
    },
  });

 
   const contentType = response.headers.get("content-type");
  const hasJson = contentType && contentType.includes("application/json");

  const data = hasJson ? await response.json() : null;

  if (!response.ok) {
    throw new Error(data.message || 'Something went wrong');
  }

  return data;
}

export async function fetchApiWithBlob(endpoint, options = {}) {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Something went wrong');
  }

  return response.blob();
}