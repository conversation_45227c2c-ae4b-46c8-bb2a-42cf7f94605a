import { useState } from "react";
import { SubTabs } from "./SubTab";
import { SMSNotificationByRole, SMSNotificationByID } from "./SmsNotification";
import { EmailNotificationByRole, EmailNotificationByID } from "./EmailNotification";
import { IDNotificationByRole, IDNotificationByID } from "./AppNotifications";

export function NotificationList({ activeTab }) {
  const [activeSubTab, setActiveSubTab] = useState("By Users");

  return (
    <div className="bg-white rounded-2xl shadow-lg p-8 max-w-lg mx-auto border border-gray-200">
      <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
        {activeTab}
      </h2>


      <SubTabs activeSubTab={activeSubTab} setActiveSubTab={setActiveSubTab} />


      <div className="mt-6">

        {activeTab === "SMS Notification" && activeSubTab === "By Users" && <SMSNotificationByRole />}
        {activeTab === "SMS Notification" && activeSubTab === "By ID" && <SMSNotificationByID />}


        {activeTab === "Email Notification" && activeSubTab === "By Users" && <EmailNotificationByRole />}
        {activeTab === "Email Notification" && activeSubTab === "By ID" && <EmailNotificationByID />}


        {activeTab === "App Notification" && activeSubTab === "By Users" && <IDNotificationByRole />}
        {activeTab === "App Notification" && activeSubTab === "By ID" && <IDNotificationByID />}
      </div>
    </div>
  );
}
