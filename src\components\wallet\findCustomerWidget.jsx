
import React, { useState } from "react";
import { findCustomer } from "../../lib/wallet";
import { toast } from "react-toastify";

const FindCustomerWidget = ({ label, onCustomerFound }) => {
  const [email, setEmail] = useState("");
  const [customerData, setCustomerData] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleFindCustomer = async () => {
    if (!email) {
      toast.error("Please enter a valid email.");
      return;
    }
    setLoading(true);
    try {
      const response = await findCustomer(email);
      setCustomerData(response);
      onCustomerFound && onCustomerFound(response);
      toast.success("Customer found");
    } catch (error) {
      console.error("Error finding customer:", error);
      toast.error("Error finding customer");
      setCustomerData(null);
      onCustomerFound && onCustomerFound(null);
    }
    setLoading(false);
  };

  return (
    <div className="bg-gray-50 p-4 rounded-md shadow mb-4">
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      <div className="flex gap-2 mt-2">
        <input
          type="email"
          placeholder="Enter customer email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="flex-1 border rounded-md p-2"
        />
        <button
          onClick={handleFindCustomer}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
        >
          {loading ? "Searching..." : "Find"}
        </button>
      </div>
      {customerData && (
        <div className="mt-2">
          <h3 className="font-semibold">Customer Details</h3>
          <pre className="bg-white p-2 rounded border text-xs overflow-auto max-h-40">
            {JSON.stringify(customerData, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default FindCustomerWidget;
