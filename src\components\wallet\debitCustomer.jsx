import React, { useState } from 'react';
import { toast } from 'react-toastify';
import { debitCustomer } from '../../lib/wallet';
import FindCustomerWidget from './findCustomerWidget';

function DebitCustomer() {
  const [isLoading, setIsLoading] = useState(false);
  const [customerId, setCustomerId] = useState("");
  const [debitDetails, setDebitDetails] = useState({
    amount: '',
    customerId: ""
  });

  const handleInput = (e) => {
    const { name, value } = e.target;
    setDebitDetails((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const response = await debitCustomer({
        amount: Number(debitDetails.amount),
        customerId: debitDetails.customerId
      });
      if (response) {
        toast.success("Debit successful");

      }
    } catch (error) {
      console.error(error);
      toast.error("Debit transaction failed");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white relative rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold mb-4">Debit Customer</h2>

      <FindCustomerWidget
        label="Customer Email"
        onCustomerFound={(data) => {
          const customer = data?.user?.virtualAccount;
          if (customer?.customer_id) {
            setCustomerId(customer.customer_id);
            setDebitDetails((prev) => ({
              ...prev,
              customerId: customer.customer_id,
            }));
          }
        }}
      />

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Amount</label>
          <input
            type="number"
            value={debitDetails.amount}
            onChange={handleInput}
            name="amount"
            step="0.01"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>

        <button
          type="submit"
          disabled={isLoading || !customerId || !debitDetails.amount}
          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          {isLoading ? 'Processing...' : 'Debit Customer'}
        </button>
      </form>
    </div>
  );
}

export default DebitCustomer;
