import React, { useState } from 'react';
import { customerToCustomer } from '../../lib/wallet';
import { toast } from 'react-toastify';
import FindCustomerWidget from './findCustomerWidget';

function CustomerToCustomer() {
  const [isLoading, setIsLoading] = useState(false);
  const [customerDetails, setCustomerDetails] = useState({
    amount: '',
    fromCustomerId: '',
    toCustomerId: '',
    narration: '',
  });


  const [fromCustomerData, setFromCustomerData] = useState(null);
  const [toCustomerData, setToCustomerData] = useState(null);

  const handleFromCustomerFound = (data) => {
    if (data && data.user?.virtualAccount?.customer_id) {
      setCustomerDetails((prev) => ({
        ...prev,
        fromCustomerId: data.user.virtualAccount.customer_id,
      }));
      setFromCustomerData(data);
    } else {
      setCustomerDetails((prev) => ({
        ...prev,
        fromCustomerId: '',
      }));
      setFromCustomerData(null);
    }
  };

  const handleToCustomerFound = (data) => {
    if (data && data.user?.virtualAccount?.customer_id) {
      setCustomerDetails((prev) => ({
        ...prev,
        toCustomerId: data.user.virtualAccount.customer_id,
      }));
      setToCustomerData(data);
    } else {
      setCustomerDetails((prev) => ({
        ...prev,
        toCustomerId: '',
      }));
      setToCustomerData(null);
    }
  };

  const handleInput = (e) => {
    const { name, value } = e.target;
    setCustomerDetails((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const response = await customerToCustomer({
        amount: customerDetails.amount,
        fromCustomerId: customerDetails.fromCustomerId,
        toCustomerId: customerDetails.toCustomerId,
        narration: customerDetails.narration,
      });
      if (response) {
        toast.success('Transfer successful!');


      }
    } catch (error) {
      console.error(error);
      toast.error('Transaction failed!');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold mb-4">Customer To Customer</h2>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Amount</label>
          <input
            type="number"
            value={customerDetails.amount}
            onChange={handleInput}
            name="amount"
            step="0.01"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>


        <FindCustomerWidget
          label="From Customer Email"
          onCustomerFound={handleFromCustomerFound}
        />


        <FindCustomerWidget
          label="To Customer Email"
          onCustomerFound={handleToCustomerFound}
        />

        <div>
          <label className="block text-sm font-medium text-gray-700">Narration</label>
          <input
            type="text"
            name="narration"
            value={customerDetails.narration}
            onChange={handleInput}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>

        <button
          type="submit"
          disabled={
            isLoading ||
            !customerDetails.amount ||
            !customerDetails.fromCustomerId ||
            !customerDetails.toCustomerId
          }
          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          {isLoading ? 'Processing...' : 'Transfer Funds'}
        </button>
      </form>
    </div>
  );
}

export default CustomerToCustomer;
