import { fetchApi, fetchApiWithBlob } from './api';

/**
 * Fetch leads with pagination and filters
 * @param {Object} filters - Filter parameters
 * @param {number} filters.page - Page number (default: 1)
 * @param {number} filters.limit - Items per page (default: 20)
 * @param {string} filters.status - Filter by status: ACTIVE, INACTIVE, UNDER_REGISTRATION
 * @param {string} filters.extractionStatus - Filter by extraction status: pending, in_progress, completed, failed
 * @param {string} filters.notificationStatus - Filter by notification status: not_sent, sent, failed
 * @param {string} filters.searchTerm - Search in company name, email, or phone
 * @returns {Promise<Object>} Response with leads and pagination
 */
export const getLeads = async (filters = {}) => {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      params.append(key, value);
    }
  });

  return fetchApi(`/api/admin/leads?${params.toString()}`);
};

/**
 * Fetch a single lead by ID
 * @param {string} leadId - Lead's unique ID
 * @returns {Promise<Object>} Response with lead details
 */
export const getLeadById = async (leadId) => {
  return fetchApi(`/api/admin/leads/${leadId}`);
};

/**
 * Delete a lead by ID
 * @param {string} leadId - Lead's unique ID
 * @returns {Promise<Object>} Response message
 */
export const deleteLead = async (leadId) => {
  return fetchApi(`/api/admin/leads/${leadId}`, {
    method: 'DELETE'
  });
};

/**
 * Send email to a specific lead
 * @param {string} leadId - Lead's unique ID
 * @returns {Promise<Object>} Response with updated lead
 */
export const sendEmailToLead = async (leadId) => {
  return fetchApi(`/api/admin/leads/${leadId}/send-email`, {
    method: 'POST'
  });
};

/**
 * Send bulk email to multiple leads
 * @param {Object} filters - Filter criteria for bulk email
 * @param {string} filters.status - Filter by status
 * @param {string} filters.extractionStatus - Filter by extraction status
 * @param {string} filters.notificationStatus - Filter by notification status
 * @returns {Promise<Object>} Response with bulk email results
 */
export const sendBulkEmail = async (filters = {}) => {
  return fetchApi('/api/admin/leads/send-bulk-email', {
    method: 'POST',
    body: JSON.stringify(filters)
  });
};

/**
 * Update notification status of a lead
 * @param {string} leadId - Lead's unique ID
 * @param {Object} data - Notification data
 * @param {string} data.notificationStatus - New notification status
 * @param {string} data.notificationError - Error message if failed
 * @returns {Promise<Object>} Response with updated lead
 */
export const updateNotificationStatus = async (leadId, data) => {
  return fetchApi(`/api/admin/leads/${leadId}/notification`, {
    method: 'PUT',
    body: JSON.stringify(data)
  });
};

/**
 * Retry failed extractions
 * @param {Array<string>} leadIds - Array of lead IDs to retry (optional, retries all if not provided)
 * @returns {Promise<Object>} Response with retry results
 */
export const retryFailedExtractions = async (leadIds = null) => {
  const body = leadIds ? { leadIds } : {};
  return fetchApi('/api/admin/leads/retry-extractions', {
    method: 'POST',
    body: JSON.stringify(body)
  });
};

/**
 * Get lead statistics
 * @returns {Promise<Object>} Response with lead statistics
 */
export const getLeadStatistics = async () => {
  return fetchApi('/api/admin/leads/statistics');
};

/**
 * Export leads to CSV
 * @param {Object} filters - Filter parameters for export
 * @param {string} filters.status - Filter by status
 * @param {string} filters.extractionStatus - Filter by extraction status
 * @param {string} filters.notificationStatus - Filter by notification status
 * @returns {Promise<Blob>} CSV file blob
 */
export const exportLeadsToCsv = async (filters = {}) => {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      params.append(key, value);
    }
  });

  return fetchApiWithBlob(`/api/admin/leads/export?${params.toString()}`);
}; 