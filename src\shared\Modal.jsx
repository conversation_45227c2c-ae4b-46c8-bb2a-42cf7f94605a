import React from 'react';

const Modal = ({ isOpen, onClose, title, children, message, onConfirm }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 z-50 flex justify-center items-center">
      <div className="bg-white rounded-lg shadow-lg max-w-lg w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-2xl font-semibold text-gray-800">{title}</h3>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <div className="space-y-4">{children}</div>
        <p className="text-gray-700">{message}</p>
        <div className="mt-6 flex justify-end space-x-4">
          <button
            className="py-2 px-4 text-gray-700 border border-gray-300 rounded-lg dark:bg-gray-200 dark:text-gray-700 hover:bg-gray-200"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className="py-2 px-4 text-white bg-red-600 rounded-lg hover:bg-red-700"
            onClick={onConfirm}
          >
            Confirm
          </button>

        </div>
      </div>
    </div>
  );
};

export default Modal;
