import { useEffect, useState } from "react";
import { useCallback } from "react";
import { createEmailNotificationByID, createEmailNotificationByRole, createNotificationByRole } from "../../lib/notifications";
import { toast } from "react-toastify";
import { listUsers } from "../../lib/user";


export function EmailNotificationByRole() {
  const [notification, setNotification] = useState({
    role: "",
    subject: "",
    message: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleNotification = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const response = await createEmailNotificationByRole({
        roles: [notification.role],
        subject: notification.subject,
        message: notification.message,
      });
      if (response) {
        toast.success("Notification sent successfully");
        setNotification({
          role: "",
          subject: "",
          message: "",
        })
      }
    } catch (error) {
      console.log(error);
      toast.error("Failed to send notification");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg p-6 max-w-lg mx-auto border border-gray-200">
      <h2 className="text-xl font-bold text-gray-800 mb-4 text-center">Send Email Notification by Users</h2>
      <form onSubmit={(e) => handleNotification(e)} className="space-y-4">
        <div>
          <label className="block text-sm font-semibold text-gray-700">Select Role</label>
          <select
            value={notification.role}
            name="role"
            onChange={(e) => setNotification({ ...notification, role: e.target.value })}
            className="mt-2 block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 p-2 bg-gray-50 text-gray-700"
          >
            <option value="">Select a role</option>
            <option value="user">User</option>
            <option value="merchant">Merchant</option>
            <option value="both">Both</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-semibold text-gray-700">Subject</label>
          <input
            value={notification.subject}
            name="message"
            onChange={(e) => setNotification({ ...notification, subject: e.target.value })}
            className="mt-2 block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 p-2 bg-gray-50 text-gray-700 h-14"
          />
        </div>

        <div>
          <label className="block text-sm font-semibold text-gray-700">Message</label>
          <textarea
            value={notification.message}
            name="message"
            onChange={(e) => setNotification({ ...notification, message: e.target.value })}
            className="mt-2 relative block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 p-2 bg-gray-50 text-gray-700 h-20"
          />
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-md text-sm font-medium text-white ${isLoading ? "bg-gray-400" : "bg-indigo-600 hover:bg-indigo-700"
            } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200`}
        >
          {isLoading ? "Sending..." : "Send Notification"}
        </button>
      </form>
    </div>
  );
}






import { FaPaperPlane, FaChevronDown, FaChevronUp } from "react-icons/fa";




export function EmailNotificationByID() {
  const [users, setUsers] = useState([]);
  const [selectedUserIds, setSelectedUserIds] = useState(new Set());
  const [message, setMessage] = useState("");
  const [subject, setSubject] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);


  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await listUsers();
        console.log("Fetched Users:", response.data);
        setUsers(response.data);
      } catch (error) {
        console.error(error);
      }
    };
    fetchUsers();
  }, []);

  const toggleDropdown = () => setIsDropdownOpen(!isDropdownOpen);


  const handleCheckboxChange = (userId) => {
    setSelectedUserIds((prevSelected) => {
      const newSelected = new Set(prevSelected);
      if (newSelected.has(userId)) {
        newSelected.delete(userId);
      } else {
        newSelected.add(userId);
      }
      return new Set([...newSelected]);
    });
  };
  const filteredUsers = users.filter((user) => {
    const identifier = (user.id || user._id || user.email || "").toString().toLowerCase();
    const name = (user.name || "").toLowerCase();
    const search = searchTerm.toLowerCase();
    return identifier.includes(search) || name.includes(search);
  });
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (selectedUserIds.size === 0) {
      toast.error("Please select at least one user.");
      return;
    }
    if (!message.trim() || !subject.trim()) {
      toast.error("Fields cannot be empty.");
      return;
    }

    setIsLoading(true);
    try {
      const response = await createEmailNotificationByID({ userIds: Array.from(selectedUserIds), subject, message });
      if (response) {
        toast.success("Email Notification sent successfully!");
        setSelectedUserIds(new Set());
        setMessage("");
      }
    } catch (error) {
      console.error(error);
      toast.error("Failed to send notification.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center  bg-gray-100">
      <div className="bg-white rounded-2xl shadow-lg p-8 max-w-lg w-full border border-gray-200">
        <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">📩 Send Email Notification</h2>

        <form onSubmit={(e) => handleSubmit(e)} className="space-y-6">

          <div className="relative">
            <button
              type="button"
              onClick={toggleDropdown}
              className="w-full flex justify-between items-center p-3 bg-gray-50 border rounded-lg text-gray-700"
            >
              {selectedUserIds.size > 0
                ? `${selectedUserIds.size} Users Selected`
                : "Select Users"}
              {isDropdownOpen ? <FaChevronUp /> : <FaChevronDown />}
            </button>

            {isDropdownOpen && (
              <div className="absolute w-full bg-white border shadow-lg rounded-lg mt-2 max-h-60 overflow-y-auto z-10">
                <div className="p-2 border-b">
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search by ID or name..."
                    className="w-full p-2 border rounded-md focus:outline-none focus:border-indigo-500"
                  />
                </div>
                {filteredUsers.map((user) => {
                  const userId = user.id || user._id || user.email;
                  return (
                    <label
                      key={userId}
                      className="flex items-center space-x-3 p-2 hover:bg-gray-100 cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={selectedUserIds.has(userId)}
                        onChange={() => handleCheckboxChange(userId)}
                        className="w-4 h-4 text-indigo-600 border-gray-300 rounded"
                      />
                      <span className="text-gray-700">{user.name}</span>
                    </label>
                  );
                })}
              </div>
            )}
          </div>
          <div>
            <label className="block text-sm font-semibold text-gray-700">Subject</label>
            <textarea
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder="Type your subject..."
              className="mt-2 block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 p-3 bg-gray-50 text-gray-700 h-24 resize-none"
            />
          </div>

          <div >
            <label className="block text-sm font-semibold text-gray-700">Message</label>
            <div className="relative">
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Type your message..."
                className="mt-2 relative block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 p-3 bg-gray-50 text-gray-700 h-24 resize-none"
              />
            </div>
          </div>


          <button
            type="submit"
            disabled={isLoading}
            className={`w-full flex items-center justify-center gap-2 py-3 px-6 text-white text-lg font-medium rounded-lg shadow-md transition-all duration-200 ${isLoading ? "bg-gray-400" : "bg-indigo-600 hover:bg-indigo-700"
              }`}
          >
            {isLoading ? "Sending..." : <><FaPaperPlane /> Send Notification</>}
          </button>
        </form>
      </div>
    </div>
  );
}






