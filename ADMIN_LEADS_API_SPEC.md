# Admin Leads Management API Specification

## Overview
This document provides a comprehensive guide for implementing the admin leads management system. The API allows administrators to manage leads, send emails, and perform bulk operations.

## Base URL
```
/api/admin/leads
```

## Authentication
All endpoints require admin authentication using the `adminToken` middleware. Include the access token in the Authorization header:
```
Authorization: Bearer <admin_access_token>
```

## Endpoints

### 1. Get Leads List
**GET** `/api/admin/leads`

Retrieves a paginated list of leads with filtering and search capabilities.

#### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | Number | No | Page number (default: 1) |
| `limit` | Number | No | Items per page (default: 20) |
| `status` | String | No | Filter by status: `ACTIVE`, `INACTIVE`, `UNDER_REGISTRATION` |
| `extractionStatus` | String | No | Filter by extraction status: `pending`, `in_progress`, `completed`, `failed` |
| `notificationStatus` | String | No | Filter by notification status: `not_sent`, `sent`, `failed` |
| `searchTerm` | String | No | Search in company name, email, or phone |

#### Response
```json
{
  "message": "Leads retrieved successfully",
  "leads": [
    {
      "_id": "lead_id",
      "companyId": 12345,
      "approvedName": "Company Name Ltd",
      "rcNumber": "RC123456",
      "status": "ACTIVE",
      "email": "<EMAIL>",
      "phone": "+2348012345678",
      "taxOffice": "Tax Office Name",
      "natureOfBusiness": "Technology",
      "companyRegistrationDate": "2020-01-15T00:00:00.000Z",
      "extractionStatus": "completed",
      "notificationStatus": "not_sent",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "pages": 8
  }
}
```

### 2. Get Lead by ID
**GET** `/api/admin/leads/:id`

Retrieves a specific lead by its ID.

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | String | Yes | Lead's unique ID |

#### Response
```json
{
  "message": "Lead retrieved successfully",
  "lead": {
    "_id": "lead_id",
    "companyId": 12345,
    "approvedName": "Company Name Ltd",
    "rcNumber": "RC123456",
    "status": "ACTIVE",
    "email": "<EMAIL>",
    "phone": "+2348012345678",
    "taxOffice": "Tax Office Name",
    "natureOfBusiness": "Technology",
    "companyRegistrationDate": "2020-01-15T00:00:00.000Z",
    "extractionStatus": "completed",
    "notificationStatus": "not_sent",
    "extractionError": null,
    "notificationError": null,
    "lastExtractionAttempt": "2024-01-01T00:00:00.000Z",
    "lastNotificationAttempt": null,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 3. Delete Lead
**DELETE** `/api/admin/leads/:id`

Deletes a specific lead.

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | String | Yes | Lead's unique ID |

#### Response
```json
{
  "message": "Lead deleted successfully"
}
```

### 4. Send Email to Lead
**POST** `/api/admin/leads/:id/send-email`

Sends a personalized email to a specific lead using the email template.

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | String | Yes | Lead's unique ID |

#### Response
```json
{
  "message": "Email sent successfully",
  "lead": {
    "_id": "lead_id",
    "notificationStatus": "sent",
    "lastNotificationAttempt": "2024-01-01T12:00:00.000Z"
  }
}
```

#### Error Responses
- `400`: Lead does not have an email address
- `404`: Lead not found
- `500`: Email sending failed

### 5. Send Bulk Email
**POST** `/api/admin/leads/send-bulk-email`

Sends emails to multiple leads based on filters.

#### Request Body
```json
{
  "status": "ACTIVE",
  "extractionStatus": "completed",
  "notificationStatus": "not_sent"
}
```

#### Body Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `status` | String | No | Filter by status |
| `extractionStatus` | String | No | Filter by extraction status |
| `notificationStatus` | String | No | Filter by notification status |

#### Response
```json
{
  "message": "Bulk email process completed",
  "results": {
    "total": 50,
    "success": 48,
    "failed": 2,
    "errors": [
      {
        "leadId": "lead_id_1",
        "email": "<EMAIL>",
        "error": "Invalid email address"
      }
    ]
  }
}
```

### 6. Update Notification Status
**PUT** `/api/admin/leads/:id/notification`

Updates the notification status of a lead.

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | String | Yes | Lead's unique ID |

#### Request Body
```json
{
  "notificationStatus": "sent",
  "notificationError": "Error message if failed"
}
```

#### Body Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `notificationStatus` | String | Yes | New notification status |
| `notificationError` | String | No | Error message if failed |

#### Response
```json
{
  "message": "Notification status updated successfully",
  "lead": {
    "_id": "lead_id",
    "notificationStatus": "sent",
    "notificationError": null,
    "lastNotificationAttempt": "2024-01-01T12:00:00.000Z"
  }
}
```

### 7. Retry Failed Extractions
**POST** `/api/admin/leads/retry-extractions`

Retries failed contact information extractions for leads.

#### Request Body
```json
{
  "leadIds": ["lead_id_1", "lead_id_2"]
}
```

#### Body Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `leadIds` | Array | No | Array of lead IDs to retry (retries all if not provided) |

#### Response
```json
{
  "message": "Retry process completed",
  "results": {
    "total": 10,
    "success": 8,
    "failed": 2,
    "errors": [
      {
        "leadId": "lead_id_1",
        "error": "No RC number available"
      }
    ]
  }
}
```

### 8. Get Lead Statistics
**GET** `/api/admin/leads/statistics`

Retrieves comprehensive statistics about leads.

#### Response
```json
{
  "message": "Statistics retrieved successfully",
  "statistics": {
    "totalLeads": 150,
    "activeLeads": 120,
    "extractionStats": {
      "pending": 10,
      "completed": 130,
      "failed": 10
    },
    "contactStats": {
      "withEmail": 100,
      "withPhone": 95
    }
  }
}
```

### 9. Export Leads to CSV
**GET** `/api/admin/leads/export`

Exports leads to CSV format for download.

#### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `status` | String | No | Filter by status |
| `extractionStatus` | String | No | Filter by extraction status |
| `notificationStatus` | String | No | Filter by notification status |

#### Response
Returns a CSV file with the following columns:
- Company Name
- RC Number
- Status
- Email
- Phone
- Tax Office
- Nature of Business
- Registration Date
- Extraction Status
- Notification Status
- Created At

## Email Template

The system uses a predefined HTML email template with the following dynamic variables:

- `{{approvedName}}`: Company's approved name (falls back to "there" if not available)
- `{{currentYear}}`: Current year (automatically set)

### Template Features
- Responsive HTML design
- Professional styling
- Call-to-action button
- Contact information
- Dynamic content replacement

## Error Handling

### Common Error Responses

#### 400 Bad Request
```json
{
  "message": "Error description",
  "error": "Detailed error message"
}
```

#### 401 Unauthorized
```json
{
  "message": "Admin access only"
}
```

#### 404 Not Found
```json
{
  "message": "Lead not found"
}
```

#### 500 Internal Server Error
```json
{
  "message": "Failed to process request",
  "error": "Detailed error message"
}
```

## Rate Limiting

- Bulk email operations are processed in batches of 10 leads
- 2-second delay between batches to avoid overwhelming email services
- Individual email sending has no rate limits

## Frontend Implementation Guide

### 1. Lead List View
```javascript
// Fetch leads with filters
const fetchLeads = async (filters = {}) => {
  const params = new URLSearchParams({
    page: filters.page || 1,
    limit: filters.limit || 20,
    ...filters
  });
  
  const response = await fetch(`/api/admin/leads?${params}`, {
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  });
  
  return response.json();
};
```

### 2. Lead Modal View
```javascript
// Fetch single lead
const fetchLead = async (leadId) => {
  const response = await fetch(`/api/admin/leads/${leadId}`, {
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  });
  
  return response.json();
};
```

### 3. Send Email
```javascript
// Send email to single lead
const sendEmail = async (leadId) => {
  const response = await fetch(`/api/admin/leads/${leadId}/send-email`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    }
  });
  
  return response.json();
};
```

### 4. Bulk Email
```javascript
// Send bulk email
const sendBulkEmail = async (filters = {}) => {
  const response = await fetch('/api/admin/leads/send-bulk-email', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(filters)
  });
  
  return response.json();
};
```

### 5. Delete Lead
```javascript
// Delete lead
const deleteLead = async (leadId) => {
  const response = await fetch(`/api/admin/leads/${leadId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  });
  
  return response.json();
};
```

### 6. Export Leads
```javascript
// Export leads to CSV
const exportLeads = async (filters = {}) => {
  const params = new URLSearchParams(filters);
  const response = await fetch(`/api/admin/leads/export?${params}`, {
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  });
  
  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'leads-export.csv';
  a.click();
  window.URL.revokeObjectURL(url);
};
```

## Status Enums

### Lead Status
- `ACTIVE`: Active company
- `INACTIVE`: Inactive company
- `UNDER_REGISTRATION`: Company under registration

### Extraction Status
- `pending`: Contact extraction not started
- `in_progress`: Contact extraction in progress
- `completed`: Contact extraction completed successfully
- `failed`: Contact extraction failed

### Notification Status
- `not_sent`: Email not sent yet
- `sent`: Email sent successfully
- `failed`: Email sending failed

## Best Practices

1. **Error Handling**: Always handle API errors gracefully and show user-friendly messages
2. **Loading States**: Show loading indicators during API calls
3. **Pagination**: Implement proper pagination controls
4. **Filtering**: Provide clear filter options with proper validation
5. **Confirmation**: Ask for confirmation before destructive actions (delete, bulk email)
6. **Progress Tracking**: Show progress for bulk operations
7. **Real-time Updates**: Refresh data after successful operations
8. **Accessibility**: Ensure all UI elements are accessible
9. **Mobile Responsive**: Design for mobile and desktop usage
10. **Security**: Never expose sensitive data in client-side code

## Testing

### API Testing Checklist
- [ ] Authentication works correctly
- [ ] All endpoints return expected responses
- [ ] Error handling works for invalid inputs
- [ ] Pagination works correctly
- [ ] Filtering works for all parameters
- [ ] Search functionality works
- [ ] Email sending works with template
- [ ] Bulk operations work correctly
- [ ] CSV export works
- [ ] Statistics are accurate

### Frontend Testing Checklist
- [ ] All UI components render correctly
- [ ] User interactions work as expected
- [ ] Error messages are displayed properly
- [ ] Loading states work correctly
- [ ] Pagination controls work
- [ ] Filter controls work
- [ ] Modal dialogs work
- [ ] Form validation works
- [ ] Responsive design works
- [ ] Accessibility requirements are met 