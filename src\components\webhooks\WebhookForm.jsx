import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createWebhook, updateWebhook } from '../../lib/webhooks';

const VALID_EVENTS = [
  'transaction.initiated',
  'transaction.completed',
  'transaction.failed',
  'wallet.credited',
  'wallet.debited',
  'webhook.failed',
];

const schema = z.object({
  url: z.string().url('Invalid URL'),
  events: z.array(z.string()).min(1, 'Select at least one event'),
  status: z.enum(['active', 'inactive']),
});

export function WebhookForm({ webhook, onClose }) {
  const queryClient = useQueryClient();
  const { register, handleSubmit, formState: { errors } } = useForm({
    resolver: zodResolver(schema),
    defaultValues: webhook ? {
      url: webhook.url,
      events: webhook.events.split(','),
      status: webhook.status,
    } : {
      status: 'active',
      events: [],
    },
  });

  const mutation = useMutation({
    mutationFn: (data) => {
      const payload = {
        ...data,
        events: data.events.join(','),
      };
      return webhook
        ? updateWebhook(webhook.id, payload)
        : createWebhook(payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['webhooks']);
      onClose();
    },
  });

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg p-6 max-w-md w-full">
        <h2 className="text-lg font-medium mb-4">
          {webhook ? 'Edit Webhook' : 'Create Webhook'}
        </h2>

        <form onSubmit={handleSubmit(mutation.mutate)} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Webhook URL
            </label>
            <input
              type="text"
              {...register('url')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
            {errors.url && (
              <p className="mt-1 text-sm text-red-500">{errors.url.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Events
            </label>
            <div className="mt-2 space-y-2">
              {VALID_EVENTS.map((event) => (
                <label key={event} className="flex items-center">
                  <input
                    type="checkbox"
                    value={event}
                    {...register('events')}
                    className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                  <span className="ml-2 text-sm text-gray-600">{event}</span>
                </label>
              ))}
            </div>
            {errors.events && (
              <p className="mt-1 text-sm text-red-500">{errors.events.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Status
            </label>
            <select
              {...register('status')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={mutation.isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {mutation.isLoading ? 'Saving...' : 'Save'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}