import { fetchApi } from './api';

// Bank Transfer
export async function bankTransfer(data) {
  return fetchApi('/api/v1/admin/wallet/bank-transfer', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// Wallet to Wallet Transfer
export async function walletTransfer(data) {
  return fetchApi('/api/v1/admin/wallet/transfer', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}
export async function creditCustomer(data) {
  const response = await fetchApi('/api/v1/admin/wallets/credit-wallet', {
    method: 'POST',
    body: JSON.stringify(data),
  });
  return response
}
export async function customerToCustomer(data) {
  const response = await fetchApi('/api/v1/admin/wallets/wallet-transfer', {
    method: 'POST',
    body: JSON.stringify(data),
  });
  return response
}

export async function debitCustomer(data) {
  const response = await fetchApi('/api/v1/admin/wallets/debit-wallet', {
    method: 'POST',
    body: JSON.stringify(data),
  });
  return response
}
export async function findCustomer(email) {
  const response = await fetchApi(`/api/v1/admin/wallets/customer/find?email=${email}`, {
    method: 'GET',
   
  });
  return response.data
}


export async function getBanks() {
  const response = await fetchApi(`/api/v1/admin/wallets/banks`)
  
    return response.data
}
export async function getBankValidation(code, account) {
  const response = await fetchApi(`/api/v1/admin/wallets/validate-account?bankCode=${code}&accountNumber=${account}`)
  console.log(response)
    return response
}
export async function PostBankTransfer(data) {
  const response = await fetchApi(`/api/v1/admin/wallets/bank-transfer`, {
    method: 'POST', 
    body: JSON.stringify(data),
  });
  return response;
}



// Get Wallet Details
export async function getWalletDetails() {
  return fetchApi('/wallet');
}


export async function getTransactions(email, page) {
  if (!email) {
    throw new Error("Email is required");
  }

  return fetchApi(`/api/v1/admin/wallets/customer/transactions?email=${email}&page=${page}`, {
    method: "GET",
   
  });
}



// Credit Customer Wallet
export async function creditWallet(data) {
  return fetchApi('/wallet/credit', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// Debit Customer Wallet
export async function debitWallet(data) {
  return fetchApi('/wallet/debit', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}