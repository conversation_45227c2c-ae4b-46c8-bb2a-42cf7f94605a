import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { getDonationDetails, getDonationPayments } from '../../lib/donations';

export function DonationViewModal({ donation, onClose }) {
  const [donationDetails, setDonationDetails] = useState(null);
  const [recentPayments, setRecentPayments] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showQR, setShowQR] = useState(false);

  useEffect(() => {
    fetchDonationDetails();
    fetchRecentPayments();
  }, [donation.donationId]);

  const fetchDonationDetails = async () => {
    try {
      const response = await getDonationDetails(donation.donationId);
      setDonationDetails(response.donation);
      setStats(response.stats);
    } catch (error) {
      toast.error('Failed to fetch donation details');
      console.error('Error fetching details:', error);
    }
  };

  const fetchRecentPayments = async () => {
    try {
      const response = await getDonationPayments(donation.donationId, 1, 5);
      setRecentPayments(response.payments || []);
    } catch (error) {
      console.error('Error fetching payments:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyLink = () => {
    const shareLink = `${window.location.origin}/donate/${donation.donationId}`;
    navigator.clipboard.writeText(shareLink);
    toast.success('Share link copied to clipboard');
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const generateQRCodePlaceholder = () => {
    // Placeholder for QR code - you can replace this with actual QR generation
    const shareLink = `${window.location.origin}/donate/${donation.donationId}`;
    return (
      <div className="bg-gray-100 p-8 rounded-lg text-center">
        <div className="w-48 h-48 mx-auto bg-white border-2 border-gray-300 rounded-lg flex items-center justify-center mb-4">
          <div className="text-gray-500 text-sm">
            <div className="mb-2">QR Code</div>
            <div className="text-xs break-all px-2">{shareLink}</div>
          </div>
        </div>
        <p className="text-sm text-gray-600">Scan to donate to this campaign</p>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b border-gray-200">
          <div>
            <h3 className="text-2xl font-semibold text-gray-900">{donation.subject}</h3>
            <p className="text-sm text-gray-600">Campaign ID: {donation.donationId}</p>
          </div>
          <div className="flex items-center space-x-3">
            <span
              className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                donation.status === 'active'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}
            >
              {donation.status}
            </span>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
            >
              ×
            </button>
          </div>
        </div>

        <div className="mt-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Campaign Details */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-3">Campaign Details</h4>
              <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-500">Description</p>
                  <p className="mt-1 text-sm text-gray-900">{donationDetails?.notes || donation.notes}</p>
                </div>
                {donationDetails?.metadata?.category && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Category</p>
                    <p className="mt-1 text-sm text-gray-900 capitalize">{donationDetails.metadata.category}</p>
                  </div>
                )}
                {donationDetails?.metadata?.targetAmount && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Target Amount</p>
                    <p className="mt-1 text-sm text-gray-900">₦{donationDetails.metadata.targetAmount.toLocaleString()}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Recent Donations */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-3">Recent Donations</h4>
              <div className="bg-gray-50 p-4 rounded-lg">
                {recentPayments.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">No donations received yet</p>
                ) : (
                  <div className="space-y-3">
                    {recentPayments.map((payment) => (
                      <div key={payment._id} className="flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0">
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {payment.donorName || 'Anonymous Donor'}
                          </p>
                          <p className="text-xs text-gray-500">{formatDate(payment.createdAt)}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">₦{payment.amount.toLocaleString()}</p>
                          <p className={`text-xs ${payment.status === 'completed' ? 'text-green-600' : 'text-red-600'}`}>
                            {payment.status}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Statistics */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-3">Statistics</h4>
              <div className="bg-gray-50 p-4 rounded-lg space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Total Raised</p>
                  <p className="text-2xl font-bold text-green-600">₦{(donation.totalReceived || 0).toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Total Donors</p>
                  <p className="text-xl font-semibold text-gray-900">{donation.donorCount || 0}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Total Transactions</p>
                  <p className="text-xl font-semibold text-gray-900">{stats?.totalTransactions || 0}</p>
                </div>
                {donationDetails?.lastDonationAt && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Last Donation</p>
                    <p className="text-sm text-gray-900">{formatDate(donationDetails.lastDonationAt)}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Share Options */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-3">Share Campaign</h4>
              <div className="space-y-3">
                <button
                  onClick={handleCopyLink}
                  className="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Copy Share Link
                </button>
                <button
                  onClick={() => setShowQR(!showQR)}
                  className="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  {showQR ? 'Hide QR Code' : 'Show QR Code'}
                </button>
              </div>
              {showQR && (
                <div className="mt-4">
                  {generateQRCodePlaceholder()}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 pt-4 border-t border-gray-200 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md text-sm font-medium"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
