import { useState } from 'react';
import { toast } from 'react-toastify'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getWebhookEvents, retryWebhookEvent } from '../../lib/webhooks';

export function WebhookEvents({ webhookId }) {
  const [page, setPage] = useState(1);
  const queryClient = useQueryClient();

  const { data, isLoading } = useQuery({
    queryKey: ['webhook-events', webhookId, page],
    queryFn: () => getWebhookEvents(webhookId, { page }),
  });

  const retryMutation = useMutation({
    mutationFn: (eventId) => retryWebhookEvent(webhookId, eventId),
    onSuccess: () => {
      queryClient.invalidateQueries(['webhook-events', webhookId]);
      toast.success('Webhook event retried successfully!');
    },
    onError: (error) => {
      toast.error(`Failed to retry webhook event: ${error.message || 'Unknown error'}`);
    },
  });

  if (isLoading) return <div>Loading events...</div>;

  return (
    <div className="space-y-4">
      {data.events.map((event) => (
        <div key={event.id} className="border-b pb-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium">{event.event}</p>
              <p className="text-xs text-gray-500">
                {new Date(event.created_at).toLocaleString()}
              </p>
              <div className="mt-2">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${event.status === 'success' ? 'bg-green-100 text-green-800' :
                  event.status === 'failed' ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                  {event.status}
                </span>
                {event.attempts > 0 && (
                  <span className="ml-2 text-xs text-gray-500">
                    Attempts: {event.attempts}
                  </span>
                )}
              </div>
            </div>
            {event.status === 'failed' && (
              <button
                onClick={() => retryMutation.mutate(event.id)}
                disabled={retryMutation.isLoading}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                Retry
              </button>
            )}
          </div>
          {event.error && (
            <div className="mt-2 p-2 bg-red-50 rounded text-sm text-red-700">
              {event.error}
            </div>
          )}
          <div className="mt-2">
            <button
              onClick={() => {
                const el = document.createElement('textarea');
                el.value = JSON.stringify(event.payload, null, 2);
                document.body.appendChild(el);
                el.select();
                document.execCommand('copy');
                document.body.removeChild(el);
                alert('Payload copied to clipboard!');
              }}
              className="text-xs text-blue-600 hover:text-blue-800"
            >
              Copy Payload
            </button>
          </div>
        </div>
      ))}

      <div className="flex justify-between items-center pt-4">
        <button
          onClick={() => setPage(p => Math.max(1, p - 1))}
          disabled={page === 1}
          className="px-3 py-1 border rounded text-sm disabled:opacity-50"
        >
          Previous
        </button>
        <span className="text-sm text-gray-600">Page {page}</span>
        <button
          onClick={() => setPage(p => p + 1)}
          disabled={!data.hasMore}
          className="px-3 py-1 border rounded text-sm disabled:opacity-50"
        >
          Next
        </button>
      </div>
    </div>
  );
}