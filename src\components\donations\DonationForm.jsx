import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'react-toastify';
import { createDonation, updateDonation, getDonationDetails } from '../../lib/donations';

const schema = z.object({
  subject: z.string().min(1, 'Subject is required').max(100, 'Subject must be less than 100 characters'),
  notes: z.string().min(1, 'Description is required').max(500, 'Description must be less than 500 characters'),
  targetAmount: z.string().optional(),
  category: z.string().optional(),
});

export function DonationForm() {
  const { donationId } = useParams();
  const navigate = useNavigate();
  const isEditing = !!donationId;
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(isEditing);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
  } = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      subject: '',
      notes: '',
      targetAmount: '',
      category: '',
    },
  });

  useEffect(() => {
    if (isEditing) {
      fetchDonationDetails();
    }
  }, [donationId, isEditing]);

  const fetchDonationDetails = async () => {
    try {
      setInitialLoading(true);
      const response = await getDonationDetails(donationId);
      const donation = response.donation;
      
      setValue('subject', donation.subject);
      setValue('notes', donation.notes);
      setValue('targetAmount', donation.metadata?.targetAmount?.toString() || '');
      setValue('category', donation.metadata?.category || '');
    } catch (error) {
      toast.error('Failed to fetch donation details');
      navigate('/donations');
    } finally {
      setInitialLoading(false);
    }
  };

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      
      const payload = {
        subject: data.subject,
        notes: data.notes,
        metadata: {
          ...(data.targetAmount && { targetAmount: parseFloat(data.targetAmount) }),
          ...(data.category && { category: data.category }),
        },
      };

      if (isEditing) {
        await updateDonation(donationId, payload);
        toast.success('Donation campaign updated successfully');
      } else {
        const response = await createDonation(payload);
        toast.success('Donation campaign created successfully');
        navigate('/donations');
        return;
      }
      
      navigate('/donations');
    } catch (error) {
      toast.error(isEditing ? 'Failed to update donation' : 'Failed to create donation');
      console.error('Error submitting form:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/donations');
  };

  if (initialLoading) {
    return (
      <div className="max-w-2xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto py-6 sm:px-6 lg:px-8">
      <div className="px-4 sm:px-0">
        <h1 className="text-2xl font-semibold text-gray-900">
          {isEditing ? 'Edit Donation Campaign' : 'Create New Donation Campaign'}
        </h1>
        <p className="mt-1 text-sm text-gray-600">
          {isEditing 
            ? 'Update your donation campaign details below.'
            : 'Create a new donation campaign to start receiving donations.'
          }
        </p>
      </div>

      <div className="mt-8">
        <div className="bg-white shadow sm:rounded-lg">
          <form onSubmit={handleSubmit(onSubmit)} className="px-4 py-5 sm:p-6 space-y-6">
            <div>
              <label htmlFor="subject" className="block text-sm font-medium text-gray-700">
                Campaign Title *
              </label>
              <input
                {...register('subject')}
                type="text"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                placeholder="e.g., Help Build a School"
              />
              {errors.subject && (
                <p className="mt-1 text-sm text-red-500">{errors.subject.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                Campaign Description *
              </label>
              <textarea
                {...register('notes')}
                rows={4}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                placeholder="Describe your donation campaign and what the funds will be used for..."
              />
              {errors.notes && (
                <p className="mt-1 text-sm text-red-500">{errors.notes.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="targetAmount" className="block text-sm font-medium text-gray-700">
                  Target Amount (₦)
                </label>
                <input
                  {...register('targetAmount')}
                  type="number"
                  min="0"
                  step="0.01"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                  placeholder="e.g., 1000000"
                />
                <p className="mt-1 text-xs text-gray-500">Optional target amount for your campaign</p>
              </div>

              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                  Category
                </label>
                <select
                  {...register('category')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                >
                  <option value="">Select a category</option>
                  <option value="education">Education</option>
                  <option value="healthcare">Healthcare</option>
                  <option value="community">Community Development</option>
                  <option value="emergency">Emergency Relief</option>
                  <option value="environment">Environment</option>
                  <option value="sports">Sports</option>
                  <option value="arts">Arts & Culture</option>
                  <option value="religion">Religious</option>
                  <option value="other">Other</option>
                </select>
                <p className="mt-1 text-xs text-gray-500">Optional category for your campaign</p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={handleCancel}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting || loading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting || loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {isEditing ? 'Updating...' : 'Creating...'}
                  </div>
                ) : (
                  isEditing ? 'Update Campaign' : 'Create Campaign'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
