import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getFeedComments, createFeedComment, updateFeedComment, deleteFeedComment } from '../../lib/feeds';

export function FeedComments({ feedId }) {
  const [newComment, setNewComment] = useState('');
  const [editingComment, setEditingComment] = useState(null);
  const queryClient = useQueryClient();

  const { data: comments, isLoading } = useQuery({
    queryKey: ['feedComments', feedId],
    queryFn: () => getFeedComments(feedId),
  });

  const createMutation = useMutation({
    mutationFn: (data) => createFeedComment(feedId, data),
    onSuccess: () => {
      queryClient.invalidateQueries(['feedComments', feedId]);
      setNewComment('');
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ commentId, data }) => updateFeedComment(feedId, commentId, data),
    onSuccess: () => {
      queryClient.invalidateQueries(['feedComments', feedId]);
      setEditingComment(null);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (commentId) => deleteFeedComment(feedId, commentId),
    onSuccess: () => {
      queryClient.invalidateQueries(['feedComments', feedId]);
    },
  });

  if (isLoading) return <div>Loading comments...</div>;

  return (
    <div className="mt-4 space-y-4">
      <div className="border-t pt-4">
        <textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Write a comment..."
          className="w-full p-2 border rounded-md"
          rows="2"
        />
        <button
          onClick={() => createMutation.mutate({ content: newComment })}
          disabled={!newComment.trim()}
          className="mt-2 px-3 py-1 bg-blue-600 text-white rounded-md text-sm disabled:opacity-50"
        >
          Comment
        </button>
      </div>

      {comments.map((comment) => (
        <div key={comment.id} className="pl-4 border-l">
          {editingComment?.id === comment.id ? (
            <div className="space-y-2">
              <textarea
                value={editingComment.content}
                onChange={(e) => setEditingComment({ ...editingComment, content: e.target.value })}
                className="w-full p-2 border rounded-md"
                rows="2"
              />
              <div className="flex space-x-2">
                <button
                  onClick={() => updateMutation.mutate({
                    commentId: comment.id,
                    data: { content: editingComment.content }
                  })}
                  className="px-2 py-1 bg-blue-600 text-white rounded-md text-sm"
                >
                  Save
                </button>
                <button
                  onClick={() => setEditingComment(null)}
                  className="px-2 py-1 bg-gray-200 rounded-md text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div>
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium">{comment.commented_by_name}</p>
                  <p className="text-sm text-gray-600">{comment.content}</p>
                  <p className="text-xs text-gray-500">
                    {new Date(comment.createdAt).toLocaleString()}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setEditingComment(comment)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => deleteMutation.mutate(comment.id)}
                    className="text-sm text-red-600 hover:text-red-800"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}