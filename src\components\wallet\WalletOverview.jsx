import { useQuery } from '@tanstack/react-query';
import { getWalletDetails } from '../../lib/wallet';

export function WalletOverview() {
  const { data: wallet, isLoading, error } = useQuery({
    queryKey: ['wallet'],
    queryFn: getWalletDetails,
  });

  if (isLoading) return <div>Loading wallet details...</div>;
  if (error) return <div>Error loading wallet: {error.message}</div>;

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-2xl font-semibold mb-4">Wallet Overview</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="p-4 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-500">Available Balance</p>
          <p className="text-2xl font-bold">₦{wallet.availableBalance.toLocaleString()}</p>
        </div>
        <div className="p-4 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-500">Account Number</p>
          <p className="text-xl font-semibold">{wallet.accountNumber}</p>
          <p className="text-sm text-gray-500">{wallet.bankName}</p>
        </div>
      </div>
    </div>
  );
}