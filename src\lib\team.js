import { fetchApi } from './api';
import { fetchApiWithBlob } from './api';

export async function createTeam(data) {
  return fetchApi('/api/v1/teams/invite', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

export async function updateTeam(teamId, data) {
  return fetchApi(`/teams/${teamId}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

export async function deleteTeam(teamId) {
  return fetchApi(`/teams/${teamId}`, {
    method: 'DELETE',
  });
}

export async function getTeams({ search = '', page = 1, limit = 30 } = {}) {
  const queryParams = new URLSearchParams();
  if (search) queryParams.append('search', search);
  queryParams.append('page', String(page));
  queryParams.append('limit', String(limit));
  
  const response = await fetchApi(`/api/v1/admin/teams?${queryParams.toString()}`);
  return response;
}



export async function getTeamMembers(teamId) {
  const response = await fetchApi(`/api/v1/admin/teams/${teamId}/users`);
  return response
}
export async function getExportTeamMembers(teamId) {
  return fetchApiWithBlob(`/api/v1/admin/teams/${teamId}/export/csv`, {
    method: "GET",
  });
}


// export async function getExportTeamMembers(teamId, defaultTeam) {
//   return fetchApi(`/api/v1/admin/teams/${teamId}/export/csv`, {
//     method: 'GET',
//     body: JSON.stringify(defaultTeam),
//   })
  
// }


export async function inviteTeamMember(teamId) {
 const response = await fetchApi(`/api/v1/admin/teams/${teamId}/invites`)
    return response
}

export async function removeTeamMember(teamId, userId) {
  const response = await fetchApi(`/api/v1/admin/teams/${teamId}/users/${userId}`, {
    method: 'DELETE', 
  });
  return response;
}
export async function cancelInvite(teamId, userId) {
  const response = await fetchApi(`/api/v1/admin/teams/${teamId}/invites/${userId}`, {
    method: 'DELETE', 
  });
  return response;
}


export async function updateTeamMemberRole(teamId, userId, role) {
  return fetchApi(`/teams/${teamId}/users/${userId}`, {
    method: 'PUT',
    body: JSON.stringify({ role }),
  });
}