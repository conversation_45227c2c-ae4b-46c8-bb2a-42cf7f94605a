import React from "react";
import { BankTransfer } from "../components/wallet/BankTransfer";
import CustomerToCustomer from "../components/wallet/CustomerToCustomer";
import CreditCustomer from "../components/wallet/creditCustomer";
import DebitCustomer from "../components/wallet/debitCustomer";
import AccordionItem from "../shared/Accordion";

export function Wallet() {
  return (
    <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div className="px-4 sm:px-0">
        <h1 className="text-2xl font-semibold text-gray-900">Wallet Management</h1>
      </div>

      <div className="mt-8 space-y-8">
        <AccordionItem title="Bank Transfer">
          <BankTransfer />
        </AccordionItem>
        <AccordionItem title="Customer To Customer">
          <CustomerToCustomer />
        </AccordionItem>
        <AccordionItem title="Credit Customer">
          <CreditCustomer />
        </AccordionItem>
        <AccordionItem title="Debit Customer">
          <DebitCustomer />
        </AccordionItem>
      </div>
    </div>
  );
}
