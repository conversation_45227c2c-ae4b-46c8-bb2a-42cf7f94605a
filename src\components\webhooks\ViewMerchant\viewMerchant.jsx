import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Link } from 'react-router-dom';
import { useQueryClient } from "@tanstack/react-query";

import { useFetchWebhook } from '../../../query/webhook';
import { toast } from 'react-toastify';
import { deleteWebhook } from '../../../lib/webhooks';
import Button from '../../../shared/Button';
import { FaPlus, FaTrash } from 'react-icons/fa';
import { FaEye, FaEdit } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';

function ViewMerchant() {
  const { id } = useParams();
  const queryClient = useQueryClient();
  const navigate = useNavigate()
  const [showModal, setShowModal] = useState(false);
  const [webhookId, setWebhookId] = useState('')
  const [searchTerm, setSearchTerm] = useState({
    search: "",
    page: 1,

  });
  const { data: fetchWebhook } = useFetchWebhook(searchTerm, id);

  console.log(fetchWebhook);
  const handleView = (id) => {
    navigate(`/view_webhookevent/${id}`)
  }
  const handleEdit = (id) => {
    navigate(`/create_webhook/${id}`)
  }
  useEffect(() => {
    setSearchTerm(prev => ({ ...prev, page: 1 }));
  }, [setSearchTerm.search]);

  const handlePageChange = (direction) => {
    setSearchTerm(prev => ({ ...prev, page: prev.page + direction }));
  };
  const handleShow = (id) => {
    setShowModal(true)
    setWebhookId(id)
  }

  const handleSearch = (e) => {
    const { name, value } = e.target
    setSearchTerm((prev) => ({
      ...prev,
      [name]: value
    }))
  }
  const handleDelete = async () => {
    try {
      const response = await deleteWebhook(webhookId)
      if (response) {
        toast.success("Webhook deleted successfully")
        setShowModal(false)
        queryClient.invalidateQueries(["webhookmerchant"]);
      }

    } catch (error) {
      console.log(error)

    }
  }



  return (
    <div className="p-4">
      <div className='flex justify-between'>
        <h2 className="text-xl font-bold mb-4">Webhook List</h2>
        <div>
          <Link to={`/create_webhook/${id}`}><Button label="Create Webhook" icon={FaPlus} variant="success" size="md" /></Link>
        </div>
      </div>
      <input
        type="text"
        placeholder="Search merchants..."
        value={searchTerm.search}
        name="search"
        onChange={handleSearch}
        className="mb-4 w-full p-2 mt-5 border border-gray-300 rounded-md shadow-sm"
      />
      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm">
          <thead className="bg-gray-100 border-b">
            <tr>
              <th className="py-2 px-4 text-left">Id</th>
              <th className="py-2 px-4 text-left">Url</th>
              <th className="py-2 px-4 text-left">Event</th>


              <th className="py-2 px-4 text-left">Status</th>
              <th className="py-2 px-4 text-left">Action</th>
            </tr>
          </thead>
          <tbody>
            {fetchWebhook?.data?.length > 0 ? (
              fetchWebhook?.data?.map((merchant) => (
                <tr key={merchant._id} className="border-b hover:bg-gray-50">
                  <td className="py-2 px-4">{merchant._id}</td>
                  <td className="py-2 px-4">{merchant.url}</td>
                  <td className="py-2 px-4 break-words max-w-[200px]">{merchant.events}</td>

                  <td className="py-2 px-4">
                    <button
                      className={`w-24 h-10 text-white font-medium rounded-lg flex justify-center items-center transition duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 
      ${merchant.status === "inactive" ? "bg-red-600 hover:bg-red-700 focus:ring-red-400" : "bg-green-600 hover:bg-green-700 focus:ring-green-400"}`}
                      aria-label="View"
                    >
                      {merchant.status}
                    </button>
                  </td>
                  <td className="py-2 px-4 relative">
                    <div className='flex gap-2 relative'>
                      <button
                        className="w-17 p-3 h-10 bg-green-600 text-white border border-gray-300 rounded-lg flex justify-center items-center hover:bg-green-900 focus:outline-none"
                        aria-label="View"
                        onClick={() => handleView(merchant._id)}


                      >
                        <FaEye />
                      </button>
                      <button
                        className="w-17 p-3 dark-bg-gray-100 dark-text-white h-10 bg-red-600 text-white border border-gray-300 rounded-lg flex justify-center items-center hover:bg-red-900 focus:outline-none"
                        aria-label="View"

                        onClick={() => handleShow(merchant._id)}


                      >
                        <FaTrash />
                      </button>
                      <button
                        className="w-17 p-3 h-10 bg-green-600 text-white border border-gray-300 rounded-lg flex justify-center items-center hover:bg-green-900 focus:outline-none"
                        aria-label="View"
                        onClick={() => handleEdit(merchant._id)}


                      >
                        <FaEdit />
                      </button>
                    </div>
                  </td>


                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="text-center py-4">
                  No webhook found
                </td>
              </tr>
            )}


          </tbody>
        </table>
      </div>
      <div className="flex justify-between items-center mt-4">
        <button
          onClick={() => handlePageChange(-1)}
          disabled={searchTerm.page === 1}
          className="px-4 py-2 border rounded-md text-gray-700 disabled:opacity-50"
        >
          Previous
        </button>
        <span className="text-sm text-gray-500">Page {searchTerm.page}</span>
        <button
          onClick={() => handlePageChange(1)}
          disabled={searchTerm.page === fetchWebhook?.totalPages || searchTerm.page > fetchWebhook?.totalPages}
          className="px-4 py-2 border rounded-md text-gray-700 disabled:opacity-50"
        >
          Next
        </button>
      </div>
      {showModal && (
        <div className="fixed inset-0 z-50 flex justify-center items-center bg-gray-900 bg-opacity-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-96">
            <h2 className="text-xl font-bold mb-4">Are you sure you want to delete?</h2>

            <div className="flex space-x-4 mt-4 justify-center">
              <button onClick={handleDelete} className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">
                Yes, Delete
              </button>
              <button onClick={() => setShowModal(false)} className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )

}

export default ViewMerchant;
