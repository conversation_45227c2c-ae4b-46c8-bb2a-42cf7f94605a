export function NotificationTabs({ activeTab, setActiveTab }) {
  const tabs = ["SMS", "Email", "In-App"];

  return (
    <div className="flex border-b mb-4">
      {tabs.map((tab) => (
        <button
          key={tab}
          onClick={() => setActiveTab(tab)}
          className={`px-4 py-2 text-sm font-medium ${activeTab === tab
            ? "border-b-2 border-indigo-600 text-indigo-600"
            : "text-gray-500"
            }`}
        >
          {tab}
        </button>
      ))}
    </div>
  );
}
