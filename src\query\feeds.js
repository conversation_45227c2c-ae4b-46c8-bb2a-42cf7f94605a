import { useQuery } from "@tanstack/react-query"
import { getFeeds, getOneFeed, getOneFeedComments, getOneFeedReply } from "../lib/feeds"

export const useFetchFeeds= () => {
   
    return useQuery({
        queryFn: () => getFeeds(),
        queryKey: ["feeds"],
        
        
    })

}

export const useFetchSingleFeed= (id) => {
   
    return useQuery({
        queryFn: () => getOneFeed(id),
        queryKey: ["singlefeeds", id],
        
        
    })

}
export const useFetchSingleFeedReply= (id) => {
   
    return useQuery({
        queryFn: () => getOneFeedReply(id),
        queryKey: ["singlefeedsReply", id],
        
        
    })

}
export const useFetchSingleFeedComments= (id) => {
   
    return useQuery({
        queryFn: () => getOneFeedComments(id),
        queryKey: ["singlefeedComment", id],
        
        
    })

}