import React from "react";

const WebhookDetailsModal = ({ isOpen, onClose, webhookData }) => {
  if (!isOpen || !webhookData) return null;
  console.log(webhookData);

  const { event, status, error, response, created_at, last_attempt, payload } = webhookData;
  const { transaction } = payload || {};

  let errorMessage = webhookData?.error;
  let errorData = null;

  if (typeof errorMessage === "string") {
    try {
      errorData = JSON.parse(errorMessage);
    } catch (e) {
      errorData = { message: errorMessage }; // Use plain string as fallback
    }
  }

  return (
    <div className="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white rounded-lg shadow-lg max-w-3xl w-full p-6 overflow-auto max-h-[90vh]">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-2xl font-semibold text-gray-800">Webhook Event Details</h3>
          <button className="text-gray-500 hover:text-gray-700" onClick={onClose}>&times;</button>
        </div>

        <div className="overflow-x-auto">
          <table className="table-fixed w-full bg-white border border-gray-200 rounded-lg shadow-sm">
            <tbody>
              <tr className="border-b odd:bg-gray-50">
                <td className="py-3 px-6 w-1/2 font-semibold">Event:</td>
                <td className="py-3 px-6 w-1/2">{event}</td>
              </tr>
              <tr className="border-b odd:bg-gray-50">
                <td className="py-3 px-6 font-semibold">Status:</td>
                <td className="py-3 px-6">
                  <span className={`px-3 py-1 rounded-full text-white text-sm ${status === "failed" ? "bg-red-600" : "bg-green-600"}`}>{status}</span>
                </td>
              </tr>
              {error ? <tr className="border-b odd:bg-gray-50">
                <td className="py-3 px-6 font-semibold">Error:</td>
                <td className="py-3 px-6 text-red-600">{errorData?.message || "No errors"}</td>
              </tr> : <tr className="border-b odd:bg-gray-50">
                <td className="py-3 px-6 font-semibold">Response:</td>
                <td className="py-3 px-6 text-red-600">{response || "No errors"}</td>
              </tr>}
              <tr className="border-b odd:bg-gray-50">
                <td className="py-3 px-6 font-semibold">Created At:</td>
                <td className="py-3 px-6">{new Date(created_at).toLocaleString()}</td>
              </tr>
            </tbody>
          </table>
        </div>

        {transaction && (
          <>
            <h4 className="text-lg font-semibold mt-6 mb-2">Transaction Details</h4>
            <div className="overflow-x-auto">
              <table className="table-fixed w-full bg-white border border-gray-200 rounded-lg shadow-sm">
                <tbody>
                  <tr className="border-b odd:bg-gray-50">
                    <td className="py-3 px-6 w-1/2 font-semibold">Amount:</td>
                    <td className="py-3 px-6 w-1/2">{transaction.amount} {transaction.currency}</td>
                  </tr>
                  <tr className="border-b odd:bg-gray-50">
                    <td className="py-3 px-6 font-semibold">Description:</td>
                    <td className="py-3 px-6">{transaction.description}</td>
                  </tr>
                  <tr className="border-b odd:bg-gray-50">
                    <td className="py-3 px-6 font-semibold">Email:</td>
                    <td className="py-3 px-6">{transaction.email}</td>
                  </tr>
                  <tr className="border-b odd:bg-gray-50">
                    <td className="py-3 px-6 font-semibold">Transaction ID:</td>
                    <td className="py-3 px-6 text-sm break-words">{transaction.id}</td>
                  </tr>
                </tbody>
              </table>
            </div>

          </>
        )}
        <div>
          <h3 className="text-lg font-semibold mt-6">Raw Data</h3>
          <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto break-words">
            {JSON.stringify(webhookData, null, 2)}
          </pre>
        </div>

        <div className="mt-6 flex justify-end space-x-4">
          <button className="py-2 px-4 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-200" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default WebhookDetailsModal;
