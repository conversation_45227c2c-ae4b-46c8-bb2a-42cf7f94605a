import PropTypes from 'prop-types';

const LeadModal = ({ isOpen, onClose, lead }) => {
  if (!isOpen || !lead) return null;

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'INACTIVE':
        return 'bg-red-100 text-red-800';
      case 'UNDER_REGISTRATION':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getExtractionStatusBadgeClass = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getNotificationStatusBadgeClass = (status) => {
    switch (status) {
      case 'sent':
        return 'bg-green-100 text-green-800';
      case 'not_sent':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 z-50 flex justify-center items-center p-4">
      <div className="bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h3 className="text-2xl font-semibold text-gray-800">Lead Details</h3>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Company Information */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">Company Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">Company Name</label>
                <p className="mt-1 text-sm text-gray-900">{lead.approvedName || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">RC Number</label>
                <p className="mt-1 text-sm text-gray-900">{lead.rcNumber || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Company ID</label>
                <p className="mt-1 text-sm text-gray-900">{lead.companyId || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Status</label>
                <div className="mt-1">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeClass(lead.status)}`}>
                    {lead.status || 'N/A'}
                  </span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Tax Office</label>
                <p className="mt-1 text-sm text-gray-900">{lead.taxOffice || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Nature of Business</label>
                <p className="mt-1 text-sm text-gray-900">{lead.natureOfBusiness || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Registration Date</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(lead.companyRegistrationDate)}</p>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">Email</label>
                <p className="mt-1 text-sm text-gray-900">{lead.email || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Phone</label>
                <p className="mt-1 text-sm text-gray-900">{lead.phone || 'N/A'}</p>
              </div>
            </div>
          </div>

          {/* System Status */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">System Status</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">Extraction Status</label>
                <div className="mt-1">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getExtractionStatusBadgeClass(lead.extractionStatus)}`}>
                    {lead.extractionStatus || 'N/A'}
                  </span>
                </div>
                {lead.extractionError && (
                  <p className="mt-1 text-xs text-red-600">{lead.extractionError}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Notification Status</label>
                <div className="mt-1">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getNotificationStatusBadgeClass(lead.notificationStatus)}`}>
                    {lead.notificationStatus || 'N/A'}
                  </span>
                </div>
                {lead.notificationError && (
                  <p className="mt-1 text-xs text-red-600">{lead.notificationError}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Last Extraction Attempt</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(lead.lastExtractionAttempt)}</p>
              </div>
            </div>
          </div>

          {/* Timestamps */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">Timestamps</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">Created At</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(lead.createdAt)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Updated At</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(lead.updatedAt)}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

LeadModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  lead: PropTypes.shape({
    _id: PropTypes.string,
    companyId: PropTypes.number,
    approvedName: PropTypes.string,
    rcNumber: PropTypes.string,
    status: PropTypes.string,
    email: PropTypes.string,
    phone: PropTypes.string,
    taxOffice: PropTypes.string,
    natureOfBusiness: PropTypes.string,
    companyRegistrationDate: PropTypes.string,
    extractionStatus: PropTypes.string,
    notificationStatus: PropTypes.string,
    extractionError: PropTypes.string,
    notificationError: PropTypes.string,
    lastExtractionAttempt: PropTypes.string,
    lastNotificationAttempt: PropTypes.string,
    createdAt: PropTypes.string,
    updatedAt: PropTypes.string
  })
};

export default LeadModal; 