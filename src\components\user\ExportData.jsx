import { useState } from 'react';
import { exportUserData } from '../../lib/user';

export function ExportData() {
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState('');

  const handleExport = async () => {
    try {
      setIsExporting(true);
      setError('');
      
      const blob = await exportUserData();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'user-data.json';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-medium text-gray-900">Export Your Data</h2>
      <p className="text-sm text-gray-500">
        Download a copy of your personal data in JSON format.
      </p>
      
      {error && (
        <div className="bg-red-50 text-red-500 p-3 rounded">{error}</div>
      )}
      
      <button
        onClick={handleExport}
        disabled={isExporting}
        className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50"
      >
        {isExporting ? 'Exporting...' : 'Export Data'}
      </button>
    </div>
  );
}