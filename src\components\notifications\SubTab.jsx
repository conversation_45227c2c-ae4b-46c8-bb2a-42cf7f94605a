export function SubTabs({ activeSubTab, setActiveSubTab }) {
  const subTabs = ["By Users", "By ID"];

  return (
    <div className="flex border-b mb-4">
      {subTabs.map((subTab) => (
        <button
          key={subTab}
          style={{ forcedColorAdjust: 'none', colorScheme: 'light' }}
          onClick={() => setActiveSubTab(subTab)}
          className={`px-4 py-2 text-sm font-medium ${activeSubTab === subTab
            ? "border-b-2 border-indigo-600 bg-indigo-600 text-white dark:border-indigo-600 dark:bg-indigo-600 dark:text-white font-semibold"
            : "text-gray-500 dark hover:text-white dark:bg-[#f9f9f9] dark:text-gray-500 hover:bg-indigo-600"
            }`}
        >
          {subTab}
        </button>
      ))}
    </div>
  );
}
