import { useQuery } from "react-query";

export function useFetchFaqs() {
  return useQuery("faqs", async () => {
    const response = await fetch("/api/faqs");
    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return response.json();
  });
}

export function useFetchSingleFaq(id) {
  return useQuery(["faq", id], async () => {
    const response = await fetch(`/api/faqs/${id}`);
    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return response.json();
  });
}
