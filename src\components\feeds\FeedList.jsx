import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getFeeds, createFeed, updateFeed, deleteFeed } from '../../lib/feeds';
import { useNavigate } from 'react-router-dom';

import { Link } from 'react-router-dom';
import { FeedComments } from './FeedComments';
import { toast } from 'react-toastify';
import Button from '../../shared/Button'
import { FaEye, FaEdit } from 'react-icons/fa';
import { FaTrash } from 'react-icons/fa';
import { FaPlus } from 'react-icons/fa';
import Modal from '../../shared/Modal'
import { useFetchFeeds } from '../../query/feeds';

export function FeedList() {
  const [page, setPage] = useState(1);
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const [feedId, setFeedId] = useState("")
  const [isModalOpenCancel, setIsModalOpenCancel] = useState(false);
  const [newPost, setNewPost] = useState('');
  const { data: fetchFeeds, isPending, isError } = useFetchFeeds()


  const [editingFeed, setEditingFeed] = useState(null);

  console.log(fetchFeeds)
  const { entity } = fetchFeeds || {};
  const { feeds = [], currentPage, total, totalPages } = entity || {};

  const handleViewFeed = (id) => {
    navigate(`/feeds/${id}`)
  }
  const handleUpdateFeed = (id) => {
    navigate(`/create_feeds/${id}`)
  }
  const handleOpenModalCancel = (id) => {

    setIsModalOpenCancel(true);
    setFeedId(id)
  };
  const handleDeleteFeed = async () => {
    try {
      const response = await deleteFeed(feedId)
      if (response) {
        setIsModalOpenCancel(false);
        toast.success("Feed deleted Successfully");
        queryClient.invalidateQueries(['feeds']);
      }

    } catch (error) {
      console.error("Error deleting member:", error);
      toast.error(error?.response?.message || error?.message || "An error occurred while deleting the member.");

    }

  }

  return (
    <div className="space-y-6">

      <div className="overflow-x-auto bg-white shadow-md rounded-lg p-4">
        <div className='flex justify-between mb-8'>
          <div>
            <h2 className="text-lg font-semibold pb-3">Feeds Overview</h2>
          </div>
          <div>
            <Link to={"/create_feeds"}><Button label="Create Feeds" icon={FaPlus} variant="indigo" size="md" /></Link>
          </div>
        </div>

        <table className="min-w-full leading-normal border border-gray-200 rounded-md">
          <thead>
            <tr className="bg-gray-100">
              <th className="px-4 py-4 border-b text-left text-xs font-bold text-black uppercase">ID</th>
              <th className="px-4 py-4 border-b text-left text-xs font-bold text-black uppercase">Title</th>

              <th className="px-4 py-4 border-b text-left text-xs font-bold text-black uppercase">Created Date</th>
              <th className="px-4 py-4 border-b text-left text-xs font-bold text-black uppercase">Updated Date</th>
              <th className="px-4 py-4 border-b text-left text-xs font-bold text-black uppercase">Action</th>
            </tr>
          </thead>
          <tbody>
            {feeds.length > 0 ? (
              feeds.map((feed, index) => (
                <tr key={index} className="bg-white">
                  <td className="px-4 py-4 border-b text-xs">{feed._id}</td>
                  <td className="px-4 py-4 border-b text-xs font-medium">{feed.name || "N/A"}</td>

                  <td className="px-4 py-4 border-b text-xs">{new Date(feed.createdAt).toLocaleDateString()}</td>
                  <td className="px-4 py-4 border-b text-xs">{new Date(feed.updatedAt).toLocaleDateString()}</td>
                  <td className="px-4 py-4 border-b text-center flex gap-3">
                    <button
                      className="w-10 h-10 bg-gray-100 border border-gray-300 rounded-lg flex justify-center items-center hover:bg-gray-200 focus:outline-none"
                      aria-label="View"
                      onClick={() => handleViewFeed(feed._id)}
                    >
                      <div>
                        <FaEye size={25} color="green" />
                      </div>
                    </button>
                    <button
                      className="w-10 h-10 bg-gray-100 border border-gray-300 rounded-lg flex justify-center items-center hover:bg-gray-200 focus:outline-none"
                      aria-label="View"
                      onClick={() => handleUpdateFeed(feed._id)}
                    >
                      <div>
                        <FaEdit size={25} color="green" />
                      </div>
                    </button>
                    <button
                      className="w-10 h-10 bg-gray-100 dark:bg-gray-100  border border-gray-300 rounded-lg flex justify-center items-center hover:bg-gray-200 focus:outline-none"
                      aria-label="View"
                      onClick={() => handleOpenModalCancel(feed._id)}
                    >
                      <div>
                        <FaTrash size={25} color="red" />
                      </div>
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="5" className="text-center py-4 text-gray-500">
                  No feeds available
                </td>
              </tr>
            )}
          </tbody>
        </table>


        <div className="mt-4 flex justify-between items-center text-xs text-gray-600">
          <p>Showing {feeds.length} of {total} feeds</p>
          <div className="flex gap-4">
            <button
              onClick={() => setPage((p) => Math.max(1, p - 1))}
              disabled={currentPage === 1}
              className="px-4 py-2 border rounded disabled:opacity-50 hover:bg-gray-200 transition"
            >
              Previous
            </button>
            <span className='mt-2'>Page {currentPage} of {totalPages}</span>
            <button
              onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
              disabled={currentPage === totalPages}
              className="px-4 py-2 border rounded disabled:opacity-50 hover:bg-gray-200 transition"
            >
              Next
            </button>
          </div>
        </div>
        <Modal
          isOpen={isModalOpenCancel}
          onClose={() => setIsModalOpenCancel(false)}
          title="Delete Feed"
          onConfirm={handleDeleteFeed}
        >
          <p className="text-gray-700">Are you sure you want to delete this feed?</p>
        </Modal>
      </div>




    </div >
  );
}