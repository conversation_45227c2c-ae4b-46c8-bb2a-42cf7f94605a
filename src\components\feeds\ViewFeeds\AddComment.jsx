import React from 'react';
import { FaUserCircle } from 'react-icons/fa';
import { IoSend } from 'react-icons/io5';

const AddComment = ({ handleAddComment, handleTextComment, newComment }) => {
  return (
    <div className="mt-6 border-t pt-4">
      <h3 className="text-lg font-semibold mb-2">Add a Comment</h3>
      <div className="flex items-center space-x-3">
        <FaUserCircle className="text-indigo-400 text-3xl" />
        <input
          type="text"
          className="w-full p-2 border rounded-lg focus:ring focus:ring-blue-200"
          placeholder="Write a comment..."
          value={newComment.content}
          name="content"
          onChange={handleTextComment}
        />
        <button
          onClick={handleAddComment}
          disabled={!newComment.content.trim()}
          className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          <IoSend className="text-xl" />
        </button>
      </div>
    </div>
  );
};

export default AddComment;
