import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { cancelInvite, inviteTeamMember } from '../../../lib/team';
import Modal from '../../../shared/Modal';
import { toast } from 'react-toastify';
import { FaTrashAlt } from 'react-icons/fa';
import { useFetchTeamInvites } from '../../../query/team';

function InviteDisplay() {
  const { id } = useParams();
  const { data: teaminvites, isPending, isError } = useFetchTeamInvites(id);
  const [userIds, setUserIds] = useState("");
  const [teamIds, setTeamIds] = useState('');
  const [isModalOpenCancel, setIsModalOpenCancel] = useState(false);

  useEffect(() => {
    const fetchInvite = async () => {
      try {
        await inviteTeamMember(id);
      } catch (error) {
        console.error(error);
      }
    };
    fetchInvite();
  }, [id]);

  const handleOpenModalCancel = (id, teamID) => {
    setUserIds(id);
    setTeamIds(teamID);
    setIsModalOpenCancel(true);
  };

  const handleConfirmCancel = async () => {
    try {
      const response = await cancelInvite(userIds, teamIds);
      if (response?.status === 200 || response?.status === 204) {
        setIsModalOpenCancel(false);
        toast.success("Invite Cancelled Successfully");
      } else {
        throw new Error("Failed to cancel invite. Please try again.");
      }
    } catch (error) {
      console.error("Error deleting member:", error);
      toast.error(error?.response?.message || error?.message || "An error occurred while deleting the member.");
    }
  };

  return (
    <div className="max-w-5xl mx-auto p-6 bg-white shadow-lg rounded-xl">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6 border-b pb-2">Team Invites</h2>
      {teaminvites?.data?.length === 0 ? (
        <p className="text-gray-500 text-center">No invites available.</p>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border border-gray-200 rounded-lg shadow-sm">
            <thead>
              <tr className="bg-gray-100 text-gray-700 text-left text-sm uppercase">
                <th className="py-3 px-4">Email</th>
                <th className="py-3 px-4">Role</th>
                <th className="py-3 px-4">Status</th>
                <th className="py-3 px-4">Created At</th>
                <th className="py-3 px-4 text-center">Action</th>
              </tr>
            </thead>
            <tbody>
              {teaminvites?.data?.map((invite) => (
                <tr key={invite._id} className="border-t hover:bg-gray-50 transition">
                  <td className="py-4 px-4 text-gray-700">{invite.email}</td>
                  <td className="py-4 px-4 capitalize text-gray-700">{invite.role}</td>
                  <td className={`py-4 px-4 font-semibold ${invite.status === "pending" ? "text-yellow-500" : "text-green-600"}`}>
                    {invite.status}
                  </td>
                  <td className="py-4 dark:bg-gray-100 px-4 text-gray-500">
                    {new Date(invite.createdAt).toLocaleDateString()}
                  </td>
                  <td className="py-4 px-4 dark:bg-gray-100 text-center">
                    <button
                      className="text-red-500 hover:text-red-700 transition"
                      onClick={() => handleOpenModalCancel(invite._id, invite.teamId)}
                    >
                      <FaTrashAlt size={18} />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      <Modal
        isOpen={isModalOpenCancel}
        onClose={() => setIsModalOpenCancel(false)}
        title="Cancel Invite"
        onConfirm={handleConfirmCancel}
      >
        <p className="text-gray-700">Are you sure you want to cancel this invite?</p>
      </Modal>
    </div>
  );
}

export default InviteDisplay;