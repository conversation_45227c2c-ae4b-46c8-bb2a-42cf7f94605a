import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { toast } from "react-toastify";
import { getAuthToken } from "../../lib/auth";

function CreateQuestion() {
  const [feature, setFeature] = useState({
    name: "",
    description: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [existingFeatures, setExistingFeatures] = useState([]);
  const navigate = useNavigate();


  useEffect(() => {
    const fetchFeatures = async () => {
      try {
        const token = getAuthToken();
        if (!token) return;

        const response = await axios.get(
          "https://api.jaraepay.us/api/v1/admin/chatbot/all-features",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.data?.data) {
          setExistingFeatures(response.data.data);
        }
      } catch (error) {
        console.error("Error fetching features:", error);
      }
    };

    fetchFeatures();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFeature((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();


    if (!feature.name.trim() || !feature.description.trim()) {
      toast.error("Please fill in all fields");
      return;
    }


    const isDuplicate = existingFeatures.some(
      (existing) => existing.name.toLowerCase() === feature.name.toLowerCase()
    );

    if (isDuplicate) {
      toast.error("A feature with this name already exists");
      return;
    }

    setIsSubmitting(true);
    const token = getAuthToken();

    try {
      const response = await axios.post(
        "https://api.jaraepay.us/api/v1/admin/chatbot/support-feature",
        {
          name: feature.name,
          description: feature.description,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.data?.message === "Feature created") {

        setFeature({
          name: "",
          description: "",
        });


        toast.success("Feature created successfully!", {
          position: "top-center",
          autoClose: 3000,
        });


        const refreshResponse = await axios.get(
          "https://api.jaraepay.us/api/v1/admin/chatbot/all-features",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setExistingFeatures(refreshResponse.data.data);

        setTimeout(() => navigate("/faq"), 1500);
      }
    } catch (error) {
      console.error("Creation error:", error);
      if (error.response) {
        if (error.response.status === 401) {
          toast.error("Session expired. Please login again.");
          navigate("/login");
        } else if (error.response.status === 409) {
          toast.error("A feature with this name already exists");
        } else {
          toast.error(
            error.response.data?.message || "Failed to create feature"
          );
        }
      } else {
        toast.error("Network error. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-10">
      <div className="max-w-2xl mx-auto bg-white p-8 rounded-xl shadow-lg">
        <h1 className="text-3xl font-semibold text-center text-gray-800 mb-8">
          Create New Support Question
        </h1>

        <form onSubmit={handleSubmit}>
          <div className="mb-6">
            <label
              htmlFor="name"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Question Name
            </label>
            <input
              id="name"
              type="text"
              name="name"
              value={feature.name}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter the question name..."
              required
              disabled={isSubmitting}
            />
          </div>

          <div className="mb-6">
            <label
              htmlFor="description"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={feature.description}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter the description..."
              rows={6}
              required
              disabled={isSubmitting}
            />
          </div>

          <div className="flex justify-center space-x-4">
            <button
              type="submit"
              className={`px-6 py-2 rounded-lg text-white ${
                isSubmitting ? "bg-blue-400" : "bg-blue-600 hover:bg-blue-700"
              } transition-colors`}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Creating...
                </>
              ) : (
                "Create Question"
              )}
            </button>
            <button
              type="button"
              onClick={() => navigate("/faq")}
              className="px-6 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
              disabled={isSubmitting}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default CreateQuestion;
