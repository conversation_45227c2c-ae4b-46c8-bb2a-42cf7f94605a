import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useFetchTransaction } from '../query/transaction';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Eye } from 'lucide-react';

const Modal = ({ isOpen, onClose, children }) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-white p-6 rounded-2xl shadow-xl max-w-lg w-full relative"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 50, opacity: 0 }}
          >
            <button onClick={onClose} className="absolute top-3 right-3 text-gray-600 hover:text-gray-900">
              <X size={24} />
            </button>
            {children}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

function Transaction() {
  const location = useLocation();
  const email = location.state?.email;
  const [currentPage, setCurrentPage] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const { data: fetchTransaction, isPending, isError } = useFetchTransaction(email, currentPage);

  const transactions = fetchTransaction?.data?.transactions || [];
  const totalPages = fetchTransaction?.data?.totalPages || 1;

  const openTransactionModal = (transaction) => {
    setSelectedTransaction(transaction);
    setShowModal(true);
  };

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="max-w-6xl mx-auto bg-white shadow-xl rounded-2xl p-6">
        <h2 className="text-2xl font-bold mb-4 text-gray-800">Transactions</h2>

        {transactions.length === 0 ? (
          <p className="text-center text-gray-500">No transactions available</p>
        ) : (
          <>
            <table className="w-full border border-gray-300 rounded-lg overflow-hidden">
              <thead>
                <tr className="bg-gray-200 text-gray-700">
                  <th className="p-3 text-left">Amount</th>
                  <th className="p-3 text-left">Type</th>
                  <th className="p-3 text-left">Status</th>
                  <th className="p-3 text-left">Reference</th>
                  <th className="p-3 text-left">Date</th>
                  <th className="p-3 text-left">Action</th>
                </tr>
              </thead>
              <tbody>
                {transactions.map((transaction) => (
                  <tr key={transaction.id} className="border-b hover:bg-gray-100">
                    <td className="p-3">{transaction.amount} {transaction.currency}</td>
                    <td className="p-3">{transaction.type}</td>
                    <td className="p-3 font-semibold text-green-600">{transaction.status}</td>
                    <td className="p-3">{transaction.reference}</td>
                    <td className="p-3">{new Date(transaction.createdAt).toLocaleDateString()}</td>
                    <td className="p-3">
                      <button onClick={() => openTransactionModal(transaction)} className="text-blue-600 hover:text-blue-800">
                        <Eye size={20} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            <div className="flex justify-between items-center mt-4">
              <button
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className={`px-4 py-2 rounded-lg ${currentPage === 1 ? 'bg-gray-300 text-gray-500' : 'bg-blue-600 text-white hover:bg-blue-800'}`}
              >
                Previous
              </button>
              <span className="text-gray-700">Page {currentPage} of {totalPages}</span>
              <button
                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className={`px-4 py-2 rounded-lg ${currentPage === totalPages ? 'bg-gray-300 text-gray-500' : 'bg-blue-600 text-white hover:bg-blue-800'}`}
              >
                Next
              </button>
            </div>
          </>
        )}
      </div>

      <Modal isOpen={showModal} onClose={() => setShowModal(false)}>
        {selectedTransaction && (
          <div>
            <h3 className="text-xl font-semibold mb-4">Transaction Details</h3>
            <div className="space-y-2 text-gray-700">
              <p><strong>ID:</strong> {selectedTransaction.id}</p>
              <p><strong>Amount:</strong> {selectedTransaction.amount} {selectedTransaction.currency}</p>
              <p><strong>Total:</strong> {selectedTransaction.total}</p>
              <p><strong>Fee:</strong> {selectedTransaction.fee}</p>
              <p><strong>VAT:</strong> {selectedTransaction.vat}</p>
              <p><strong>Type:</strong> {selectedTransaction.type}</p>
              <p><strong>Status:</strong> {selectedTransaction.status}</p>
              <p><strong>Reference:</strong> {selectedTransaction.reference}</p>
              <p><strong>Narration:</strong> {selectedTransaction.narration}</p>
              <p><strong>Balance Before:</strong> {selectedTransaction.balanceBefore}</p>
              <p><strong>Balance After:</strong> {selectedTransaction.balanceAfter}</p>
              <p><strong>Date:</strong> {new Date(selectedTransaction.createdAt).toLocaleString()}</p>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
}

export default Transaction;
