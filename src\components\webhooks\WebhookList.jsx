import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getWebhooks, deleteWebhook } from '../../lib/webhooks';
import { WebhookForm } from './WebhookForm';
import { WebhookEvents } from './WebhookEvents';

export function WebhookList() {
  const [isCreating, setIsCreating] = useState(false);
  const [editingWebhook, setEditingWebhook] = useState(null);
  const [selectedWebhook, setSelectedWebhook] = useState(null);
  const queryClient = useQueryClient();

  const { data: webhooks, isLoading } = useQuery({
    queryKey: ['webhooks'],
    queryFn: getWebhooks,
  });

  const deleteMutation = useMutation({
    mutationFn: deleteWebhook,
    onSuccess: () => {
      queryClient.invalidateQueries(['webhooks']);
      setSelectedWebhook(null);
    },
  });

  if (isLoading) return <div>Loading webhooks...</div>;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-medium">Webhooks</h2>
        <button
          onClick={() => setIsCreating(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Add Webhook
        </button>
      </div>

      {(isCreating || editingWebhook) && (
        <WebhookForm
          webhook={editingWebhook}
          onClose={() => {
            setIsCreating(false);
            setEditingWebhook(null);
          }}
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          {webhooks?.map((webhook) => (
            <div
              key={webhook.id}
              className={`bg-white shadow rounded-lg p-4 cursor-pointer ${
                selectedWebhook?.id === webhook.id ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => setSelectedWebhook(webhook)}
            >
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-medium">{webhook.url}</h3>
                  <p className="text-sm text-gray-500">Key: {webhook.key}</p>
                  <p className="text-sm text-gray-500">
                    Events: {webhook.events}
                  </p>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    webhook.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {webhook.status}
                  </span>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setEditingWebhook(webhook);
                    }}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    Edit
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      if (confirm('Are you sure you want to delete this webhook?')) {
                        deleteMutation.mutate(webhook.id);
                      }
                    }}
                    className="text-red-600 hover:text-red-800"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {selectedWebhook && (
          <div className="bg-white shadow rounded-lg p-4">
            <h3 className="font-medium mb-4">Webhook Events</h3>
            <WebhookEvents webhookId={selectedWebhook.id} />
          </div>
        )}
      </div>
    </div>
  );
}