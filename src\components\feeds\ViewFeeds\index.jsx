import React, { useState } from 'react';
import { useFetchSingleFeed, useFetchSingleFeedComments } from '../../../query/feeds';
import { useQueryClient } from "@tanstack/react-query";
import { useMutation } from '@tanstack/react-query';
import { useAuth } from '../../../contexts/AuthContext';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import FeedHeader from './FeedHeader';
import CommentList from './CommentList';
import AddComment from './AddComment';
import { createFeedComment, deleteFeedComment, getOneFeedComments } from '../../../lib/feeds';

const ViewFeeds = () => {
  const { id } = useParams();
  const queryClient = useQueryClient();
  const { data: singleFeed, refetch: refetchComments } = useFetchSingleFeed(id);
  const { user } = useAuth();
  const { data: singleFeedComment } = useFetchSingleFeedComments(id);
  const [newComment, setNewComment] = useState({ content: "", name: user?.name, parent_id: null });

  const handleTextComment = (e) => {
    const { name, value } = e.target;
    setNewComment(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddComment = async () => {
    if (!newComment.content?.trim()) return;

    try {
      const response = await createFeedComment(id, newComment);
      if (response) {
        setNewComment({ content: "", name: user?.name || "Anonymous", parent_id: null });
        toast.success("Comment added successfully");
        queryClient.invalidateQueries(["singlefeeds"]);
      }
    } catch (error) {
      console.error("Error adding comment:", error);
      toast.error("Failed to add comment");
    }
  };

  const handleAddReply = async (parentId, replyContent, setReplyContent, setActiveReply) => {
    if (!replyContent.trim()) return;
    try {
      const response = await createFeedComment(id, {
        content: replyContent,
        name: user?.name || "Anonymous",
        parent_id: parentId
      });

      if (response) {
        setReplyContent("");
        setActiveReply(null);
        toast.success("Reply added successfully");
        queryClient.invalidateQueries(["singlefeedComment"]);


      }
    } catch (error) {
      console.error("Error adding reply:", error);
      toast.error("Failed to add reply");
    }
  };
  const handleDeleteComment = async (feedId, commentId) => {
    try {
      const response = await deleteFeedComment(feedId, commentId);
      if (response) {
        toast.success("Comment deleted successfully");
        queryClient.invalidateQueries(["singlefeeds"]);

      }
    } catch (error) {
      console.error("Error deleting comment:", error);
      toast.error("Failed to delete comment");
      getOneFeedComments(id)
    }
  };


  const handleDeleteReply = async (feedId, replyId) => {
    try {
      const response = await deleteFeedComment(feedId, replyId);
      if (response) {
        toast.success("Reply deleted successfully");
        queryClient.invalidateQueries(["singlefeedComment"]);

      }
    } catch (error) {
      console.error("Error deleting reply:", error);
      toast.error("Failed to delete reply");
    }
  };

  return (
    <div className="flex justify-center bg-gray-100 p-6">
      <div className="bg-white shadow-lg rounded-2xl p-6 w-full max-w-3xl">
        <FeedHeader singleFeed={singleFeed} />
        <CommentList
          comments={singleFeedComment?.entity}
          feedId={id}
          handleDeleteComment={handleDeleteComment}
          handleDeleteReply={handleDeleteReply}
          handleAddReply={handleAddReply}
        />
        <AddComment
          handleAddComment={handleAddComment}
          handleTextComment={handleTextComment}
          newComment={newComment}
        />
      </div>
    </div>
  );
};

export default ViewFeeds;
