import React, { useState } from 'react';
import { FaUserCircle } from 'react-icons/fa';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { IoSend } from 'react-icons/io5';
import { useParams } from 'react-router-dom';

const Comment = ({ comment, showMenu, setShowMenu, handleDeleteComment, handleReplyClick, handleAddReply, handleDeleteReply }) => {
  const [replyContent, setReplyContent] = useState('');
  const [activeReply, setActiveReply] = useState(null);
  const [showReplies, setShowReplies] = useState(false);
  const { id } = useParams()

  const toggleReplies = () => setShowReplies(!showReplies);

  return (
    <div className="border-b pb-4 relative">
      <div className='flex space-x-4'>
        <FaUserCircle className="text-indigo-400 text-3xl" />
        <div>
          <p className="font-semibold">{comment.commented_by_name || "User"}</p>
          <p className="text-gray-600">{comment.content}</p>
          <p className="text-xs text-gray-400">{new Date(comment.createdAt).toLocaleString()}</p>
          {comment.children?.length > 0 && (
            <button onClick={toggleReplies} className="text-white bg-indigo-600 text-sm mt-1">
              {showReplies ? "Hide Replies" : "View Replies"} ({comment.children.length})
            </button>
          )}
        </div>
      </div>
      <div className="absolute top-0 right-0 z-50">
        <BsThreeDotsVertical
          className="text-gray-500 cursor-pointer"
          onClick={() => setShowMenu(showMenu === comment._id ? null : comment._id)}
        />
      </div>
      {showMenu === comment._id && (
        <div className="absolute top-5 right-0 mt-2 bg-white border shadow-lg rounded-lg w-32">
          <button
            className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-200"
            onClick={() => setActiveReply(comment._id)}
          >
            Reply
          </button>
          <button
            className="w-full text-left px-4 py-2 text-red-500 hover:bg-gray-200"
            onClick={() => handleDeleteComment(id, comment._id)}
          >
            Delete
          </button>
        </div>
      )}
      {activeReply === comment._id && (
        <div className="mt-4 bg-gray-50 p-4 rounded-lg shadow-md">
          <input
            type="text"
            className="w-full p-2 border rounded-lg focus:ring focus:ring-blue-200"
            placeholder="Write your reply..."
            value={replyContent}
            onChange={(e) => setReplyContent(e.target.value)}
          />
          <button
            onClick={() => handleAddReply(comment._id, replyContent, setReplyContent, setActiveReply)}
            disabled={!replyContent.trim()}
            className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 mt-2"
          >
            <IoSend className="text-xl" />
          </button>
        </div>
      )}
      {showReplies && comment?.children?.length > 0 && (
        <div className="ml-10 mt-3 border-l pl-4">
          {comment.children.map((reply) => (
            <div key={reply._id} className="relative">
              <div className="flex space-x-4">
                <FaUserCircle className="text-indigo-400 text-2xl" />
                <div>
                  <p className="font-semibold">{reply.commented_by_name || "User"}</p>
                  <p className="text-gray-600">{reply.content}</p>
                  <p className="text-xs text-gray-400">{new Date(reply.createdAt).toLocaleString()}</p>
                </div>
              </div>
              <div className="absolute top-0 right-0 z-50">
                <BsThreeDotsVertical
                  className="text-gray-500 cursor-pointer"
                  onClick={() => setShowMenu(showMenu === reply._id ? null : reply._id)}
                />
              </div>
              {showMenu === reply._id && (
                <div className="absolute top-5 right-0 mt-2 bg-white border shadow-lg rounded-lg w-32">
                  <button
                    className="w-full text-left px-4 py-2 text-red-500 hover:bg-gray-200"
                    onClick={() => handleDeleteReply(id, reply._id)}
                  >
                    Delete Reply
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Comment;



