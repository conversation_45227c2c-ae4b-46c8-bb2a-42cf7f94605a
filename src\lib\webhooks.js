import { fetchApi } from './api';

export async function getWebhooks(params = {}) {
  const queryString = new URLSearchParams(params).toString();
  return fetchApi(`/webhooks?${queryString}`);
}
export async function getMerchant(params = {}) {
  const queryString = Object.keys(params).length ? `?${new URLSearchParams(params).toString()}` : '';
  return fetchApi(`/api/v1/admin/users/merchant/list${queryString}`);
}
export async function getMerchantWebhook(params = {}, id) {
  const queryString = Object.keys(params).length ? `?${new URLSearchParams(params).toString()}` : '';
  return fetchApi(`/api/v1/admin/webhooks/merchant/${id}${queryString}`);
}

export async function getMerchantWebhookEvent(params = {}, id) {
  const queryString = Object.keys(params).length ? `?${new URLSearchParams(params).toString()}` : '';
  return fetchApi(`/api/v1/admin/webhooks/${id}/events${queryString}`);
}


export async function createWebhook(id, data) {
  return fetchApi(`/api/v1/admin/webhooks/merchant/${id}`, {
    method: 'POST',
    body: JSON.stringify(data),
  });
}


export async function updateWebhook(id, data) {
  return fetchApi(`/api/v1/admin/webhooks/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}
export async function getSingleWebHook(id) {
  const response = await fetchApi(`/api/v1/admin/webhooks/${id}`)
  
    return response
}

export async function deleteWebhook(id) {
  return fetchApi(`/api/v1/admin/webhooks/${id}`, {
    method: 'DELETE',
  });
}

// Webhook events
export async function getWebhookEvents(webhookId, params = {}) {
  const queryString = new URLSearchParams(params).toString();
  return fetchApi(`/webhooks/${webhookId}/events?${queryString}`);
}

export async function retryWebhookEvent( eventId, formInfo) {
  return fetchApi(`/api/v1/admin/webhooks/event/${eventId}/fire`, {
    method: 'POST',
     body: JSON.stringify(formInfo)
  });
}