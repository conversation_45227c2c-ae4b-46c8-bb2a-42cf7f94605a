import { useQuery } from "@tanstack/react-query"
import { getLogs } from "../lib/logs"
import { getMerchant, getMerchantWebhook, getMerchantWebhookEvent, getSingleWebHook } from "../lib/webhooks"

export const useFetchMerchant= (filter) => {
   
    return useQuery({
        queryFn: () => getMerchant(filter),
        queryKey: ["merchant", filter],
        
        
    })

}
export const useFetchSingleMerchant= (id) => {
   
    return useQuery({
        queryFn: () => getMerchant(id),
        queryKey: ["singlemerchant", id],
        
        
    })

}
export const useFetchWebhook= (filter, id) => {
   
    return useQuery({
        queryFn: () => getMerchantWebhook(filter, id),
        queryKey: ["webhookmerchant", filter, id],
        
        
    })

}
export const useFetchSingleWebhook= ( id) => {
   
    return useQuery({
        queryFn: () => getSingleWebHook( id),
        queryKey: ["webhookmerchant", id],
        
        
    })

}
export const useFetchWebhookEvent= (filter, id) => {
   
    return useQuery({
        queryFn: () => getMerchantWebhookEvent(filter, id),
        queryKey: ["webhookmerchantEvent", filter, id],
        
        
    })

}