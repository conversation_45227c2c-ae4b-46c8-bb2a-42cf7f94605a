import React from 'react';
import { FaUserCircle } from 'react-icons/fa';

const FeedHeader = ({ singleFeed }) => {
  return (
    <div className="border-b pb-6">
      <div className="flex items-center mb-4">
        <FaUserCircle className="text-indigo-500 text-4xl mr-3" />
        <div>
          <h2 className="text-lg font-semibold">{singleFeed?._id || "Anonymous"}</h2>
          <p className="text-sm text-gray-500">{new Date(singleFeed?.createdAt).toLocaleString()}</p>
          <p className="text-xs text-gray-400">{singleFeed?.like_count} likes</p>
        </div>
      </div>
      <h3 className="text-xl font-bold mb-2">{singleFeed?.name}</h3>
      <p className="text-gray-700">{singleFeed?.content}</p>
    </div>
  );
};

export default FeedHeader;
