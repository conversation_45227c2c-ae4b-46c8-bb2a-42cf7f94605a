import { Routes, Route, Link, useNavigate } from 'react-router-dom';
import { useAuth } from './contexts/AuthContext';
import { SignInForm } from './components/auth/SignInForm';
import { Profile } from './pages/Profile';
import { Teams } from './pages/Teams';
import { Wallet } from './pages/Wallet';
import { Feeds } from './pages/Feeds';
import { FAQ } from "./pages/Faq";
import CreateQuestion from "./components/faq/CreateQuestion.jsx";
import { Logs } from './pages/Logs';
import { Notifications } from './pages/Notifications';
import { Webhooks } from './pages/Webhooks';
import { Users } from './pages/Users';
import { Leads } from './pages/Leads';
import PropTypes from 'prop-types';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { signOut } from './lib/auth';
import InviteDisplay from './components/team/teamInviteDisplay/inviteDisplay';
import MemberDisplay from './components/team/teamMemberDisplay/memberDisplay';
import ViewFeeds from './components/feeds/ViewFeeds';
import CreateFeeds from './components/feeds/CreateFeed';
import ViewMerchant from './components/webhooks/ViewMerchant/viewMerchant';
import ViewWebhookEvent from './components/webhooks/ViewMerchant/viewWebhook';
import CreateWebHook from './components/webhooks/ViewMerchant/CreateWebHook';
import Transaction from './pages/Transaction';
import FAQDetail from './components/faq/FaqDetail.jsx';


function Login() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-indigo-50 via-white to-indigo-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-extrabold text-gray-900 mb-2">
            Welcome Back
          </h2>
          <p className="text-gray-600">
            Please sign in to access your account
          </p>
        </div>
        <SignInForm />
      </div>
    </div>
  );
}
function Layout({ children }) {
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    signOut();
    navigate('/login');
  };

  return (
    <div className="min-h-screen flex bg-gray-100">
      <div className="fixed inset-y-0 left-0 w-64 bg-white shadow-lg flex flex-col">
        <div className="h-16 flex items-center px-6 border-b">
          <Link to="/" className="text-xl font-bold text-gray-900">
            Dashboard
          </Link>
        </div>
        <nav className="flex-1 overflow-y-auto p-4">
          <div className="space-y-2">
            <Link
              to="/users"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-900 rounded-md hover:bg-gray-50"
            >
              Users
            </Link>
            <Link
              to="/teams"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-900 rounded-md hover:bg-gray-50"
            >
              Teams
            </Link>
            <Link
              to="/wallet"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-900 rounded-md hover:bg-gray-50"
            >
              Wallet
            </Link>
            <Link
              to="/feeds"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-900 rounded-md hover:bg-gray-50"
            >
              Feeds
            </Link>
            <Link
              to="/leads"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-900 rounded-md hover:bg-gray-50"
            >
              Leads
            </Link>
            <Link
              to="/webhooks"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-900 rounded-md hover:bg-gray-50"
            >
              Webhooks
            </Link>
            <Link
              to="/notifications"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-900 rounded-md hover:bg-gray-50"
            >
              Notifications
            </Link>
            <Link
              to="/faq"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-900 rounded-md hover:bg-gray-50"
            >
              FAQ
            </Link>
            <Link
              to="/logs"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-900 rounded-md hover:bg-gray-50"
            >
              Logs
            </Link>
          </div>
        </nav>
        <div className="p-4 border-t">
          <Link
            to="/profile"
            className="flex items-center px-4 py-2 text-sm font-medium text-gray-900 rounded-md hover:bg-gray-50"
          >
            Profile
          </Link>
          {user && (
            <button
              onClick={handleLogout}
              className="w-full mt-2 flex items-center justify-center px-4 py-2 text-sm font-medium text-red-600 rounded-md bg-gray-50 hover:bg-gray-100"
            >
              Logout
            </button>
          )}
        </div>
      </div>

      <div className="flex-1 ml-64">
        <main className="p-6">{children}</main>
      </div>
    </div>
  );
}

Layout.propTypes = {
  children: PropTypes.node.isRequired,
};

function ProtectedRoute({ children }) {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {

    return <Login />;
  }

  return <Layout>{children}</Layout>;
}

ProtectedRoute.propTypes = {
  children: PropTypes.node.isRequired,
};

function App() {
  return (
    <div>
      <ToastContainer />

      <Routes>
        <Route path="/login" element={<Login />} />
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Users />
            </ProtectedRoute>
          }
        />
        <Route
          path="/users"
          element={
            <ProtectedRoute>
              <Users />
            </ProtectedRoute>
          }
        />
        <Route
          path="/teams_invites/:id"
          element={
            <ProtectedRoute>
              <InviteDisplay />
            </ProtectedRoute>
          }
        />
        <Route
          path="/teams_members/:id"
          element={
            <ProtectedRoute>
              <MemberDisplay />
            </ProtectedRoute>
          }
        />
        <Route
          path="/teams"
          element={
            <ProtectedRoute>
              <Teams />
            </ProtectedRoute>
          }
        />
        <Route
          path="/wallet"
          element={
            <ProtectedRoute>
              <Wallet />
            </ProtectedRoute>
          }
        />
        <Route
          path="/feeds"
          element={
            <ProtectedRoute>
              <Feeds />
            </ProtectedRoute>
          }
        />
        <Route
          path="/leads"
          element={
            <ProtectedRoute>
              <Leads />
            </ProtectedRoute>
          }
        />
        <Route
          path="/create_feeds?/:id"
          element={
            <ProtectedRoute>
              <CreateFeeds />
            </ProtectedRoute>
          }
        />
        <Route
          path="/feeds/:id"
          element={
            <ProtectedRoute>
              <ViewFeeds />
            </ProtectedRoute>
          }
        />
        <Route
          path="/webhooks"
          element={
            <ProtectedRoute>
              <Webhooks />
            </ProtectedRoute>
          }
        />
        <Route
          path="/create_webhook/:id"
          element={
            <ProtectedRoute>
              <CreateWebHook />
            </ProtectedRoute>
          }
        />
        <Route
          path="/view_webhook/:id"
          element={
            <ProtectedRoute>
              <ViewMerchant />
            </ProtectedRoute>
          }
        />
        <Route
          path="/transactions"
          element={
            <ProtectedRoute>
              <Transaction />
            </ProtectedRoute>
          }
        />
        <Route
          path="/view_webhookevent/:id"
          element={
            <ProtectedRoute>
              <ViewWebhookEvent />
            </ProtectedRoute>
          }
        />
        <Route
          path="/notifications"
          element={
            <ProtectedRoute>
              <Notifications />
            </ProtectedRoute>
          }
        />
        <Route
          path="/logs"
          element={
            <ProtectedRoute>
              <Logs />
            </ProtectedRoute>
          }
        />
        <Route
          path="/profile"
          element={
            <ProtectedRoute>
              <Profile />
            </ProtectedRoute>
          }
        />
        <Route
          path="/faq"
          element={
            <ProtectedRoute>
              <FAQ />
            </ProtectedRoute>
          }
        />
        <Route
          path="/create_faq"
          element={
            <ProtectedRoute>
              <CreateQuestion />
            </ProtectedRoute>
          }
        />
        <Route
          path="/edit_faq/:id"
          element={
            <ProtectedRoute>
              <CreateQuestion />
            </ProtectedRoute>
          }
        />
        <Route
          path="/faq/:featureId"
          element={
            <ProtectedRoute>
              <FAQDetail />
            </ProtectedRoute>
          }
        />
      </Routes>
    </div>
  );
}

export default App;