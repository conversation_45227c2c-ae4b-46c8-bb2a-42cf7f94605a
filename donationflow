# Donation System API

The donation system allows merchants to create donation campaigns and users to make donations through QR codes or shareable links.

## Overview

- **Merchants** can create, manage, and track donation campaigns
- **Users** can scan QR codes or use shareable links to make donations
- **Real-time processing** with immediate wallet-to-wallet transfers
- **Comprehensive tracking** of donations and donor statistics

## Models

### Donation Model
Represents a donation campaign created by a merchant.

**Fields:**
- `merchantId`: Reference to the merchant (User with role='merchant')
- `subject`: Campaign title/subject
- `notes`: Additional campaign details
- `status`: 'active' or 'stopped'
- `donationId`: Unique public identifier (16-char nanoid)
- `totalReceived`: Total amount received
- `donorCount`: Number of unique donors
- `lastDonationAt`: Timestamp of last donation
- `metadata`: Additional flexible data

### DonationTransaction Model
Links individual donations to wallet transactions.

**Fields:**
- `donationId`: Reference to the donation campaign
- `transactionId`: Reference to the wallet transaction
- `donorId`: Reference to the donor (User)
- `merchantId`: Reference to the merchant
- `amount`: Donation amount
- `currency`: Currency (default: NGN)
- `status`: 'completed' or 'failed'

## API Endpoints

### Merchant Endpoints (Authentication Required)

#### Create Donation
```
POST /api/v1/donations
```

**Request Body:**
```json
{
  "subject": "Help Build a School",
  "notes": "We're raising funds to build a new school building",
  "metadata": {
    "category": "education",
    "targetAmount": 5000000
  }
}
```

**Response:**
```json
{
  "donationId": "abc123def456ghi7",
  "qrLink": "data:image/png;base64,...",
  "qrImageUrl": "data:image/png;base64,...",
  "shareLink": "https://yourapp.com/donate/abc123def456ghi7"
}
```

#### Update Donation Status
```
PATCH /api/v1/donations/:donationId/status
```

**Request Body:**
```json
{
  "status": "stopped"
}
```

#### Update Donation Details
```
PATCH /api/v1/donations/:donationId
```

**Request Body:**
```json
{
  "subject": "Updated Campaign Title",
  "notes": "Updated campaign description"
}
```

#### Get Donation Payments
```
GET /api/v1/donations/:donationId/payments?page=1&limit=20
```

#### Get Merchant Donation Summary
```
GET /api/v1/donations/merchant/summary
```

#### Get Merchant Donations List
```
GET /api/v1/donations/merchant/donations?page=1&limit=20&status=active
```

### Public Endpoints (No Authentication Required)

#### Get Donation Details
```
GET /api/v1/donations/:donationId
```

**Response:**
```json
{
  "donation": {
    "donationId": "abc123def456ghi7",
    "subject": "Help Build a School",
    "notes": "We're raising funds to build a new school building",
    "status": "active",
    "totalReceived": 150000,
    "donorCount": 25,
    "shareLink": "https://yourapp.com/donate/abc123def456ghi7"
  },
  "merchant": {
    "name": "John Doe",
    "businessName": "Education Foundation"
  },
  "stats": {
    "totalAmount": 150000,
    "totalTransactions": 30,
    "uniqueDonorCount": 25
  },
  "canDonate": true
}
```

### Payment Endpoints (Authentication Required)

#### Process Donation Payment
```
POST /api/v1/donations/pay/:donationId
```

**Request Body:**
```json
{
  "amount": 5000,
  "pin": "1234"
}
```

**Response:**
```json
{
  "transactionId": "507f1f77bcf86cd799439011",
  "status": "completed",
  "receiptUrl": "/transactions/DON_1234567890_abc123def",
  "donation": {
    "donationId": "abc123def456ghi7",
    "subject": "Help Build a School",
    "amount": 5000,
    "merchantName": "John Doe"
  }
}
```

## Business Rules

### Merchant Restrictions
- Only users with `role = 'merchant'` can create donations
- Merchants cannot donate to their own campaigns
- Merchants can edit donation details anytime
- Merchants can stop/start donations anytime

### Payment Processing
- Users must have sufficient wallet balance
- Users must provide valid PIN
- Donations are processed immediately (no pending state)
- Failed payments are logged but don't affect donation stats
- Each donation creates a separate wallet transaction

### QR Code Generation
- QR codes are generated on-demand (not stored)
- QR codes remain valid as long as donation is active
- QR codes contain the donation ID for scanning

### Notifications
- **Donors** receive: Database notification + SMS (debit)
- **Merchants** receive: Database notification + Email (credit)

## Security Features

- **Authentication required** for all merchant operations
- **PIN verification** for all payments
- **Role-based access control** (merchant vs user)
- **Input validation** and sanitization
- **Comprehensive logging** of all operations
- **Transaction isolation** using MongoDB sessions

## Error Handling

The API returns user-friendly error messages with appropriate HTTP status codes:

- `400 Bad Request`: Invalid input, insufficient balance, invalid PIN
- `401 Unauthorized`: Missing or invalid authentication
- `403 Forbidden`: Access denied (wrong merchant, insufficient permissions)
- `404 Not Found`: Donation not found or inactive
- `500 Internal Server Error`: Server-side errors

## Usage Examples

### Creating a Donation Campaign
1. Merchant creates donation with subject and notes
2. System generates unique donationId and QR code
3. Merchant shares QR code or shareable link

### Making a Donation
1. User scans QR code or visits shareable link
2. User enters donation amount and PIN
3. System validates donation status and user balance
4. System processes wallet-to-wallet transfer
5. Both parties receive notifications
6. Donation stats are updated

### Managing Donations
1. Merchant can view all their campaigns
2. Merchant can stop/start campaigns anytime
3. Merchant can edit campaign details
4. Merchant can view donor transactions and statistics

## Integration Points

- **Wallet System**: Uses existing wallet infrastructure
- **Transaction System**: Creates wallet-to-merchant transactions
- **Notification System**: Sends debit/credit notifications
- **QR Generation**: Uses existing QR helper functions
- **Logging**: Comprehensive audit trail for all operations

## Performance Considerations

- **Indexed fields** for fast queries
- **Pagination** for large result sets
- **Efficient aggregation** for statistics
- **Transaction batching** for database operations
- **Async notifications** to avoid blocking responses


so i need on the sidebar a donation tab

you need to follow the pattern as it is

