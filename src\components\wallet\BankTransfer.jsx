import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'react-toastify'
import { useMutation } from '@tanstack/react-query';
import { getBankValidation, PostBankTransfer } from '../../lib/wallet';
import { useFetchBanks } from '../../query/wallet';
import CreditCustomer from './creditCustomer';
import DebitCustomer from './debitCustomer';
import CustomerToCustomer from './CustomerToCustomer';


const schema = z.object({
  accountNumber: z.string().min(10, 'Account number must be at least 10 digits'),
  accountName: z.string().min(1, 'Account name is required'),
  amount: z.number().min(1, 'Amount must be greater than 0'),
  narration: z.string().optional(),
  bank: z.string().min(1, 'Bank selection is required'),
});

export function BankTransfer() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isValidating, setIsValidating] = useState(false);

  const { data: getBanks } = useFetchBanks();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(schema),
    defaultValues: { bank: '' },
  });

  const selectedBank = watch('bank');
  const accountNumber = watch('accountNumber');

  const { mutate: onMutate } = useMutation({
    mutationFn: async (values) => {
      setIsLoading(true);
      try {
        return await PostBankTransfer(values);
      } finally {
        setIsLoading(false);
      }
    },
    onSuccess: ({ data }) => {
      setSuccess(data.message);
      toast.success("Bank transfer successful")

      setIsLoading(false);

    },
    onError: (error) => {
      setError(error.message);
      setIsLoading(false);
    },
  });

  const onSubmit = (data) => {


    if (!selectedBank) {
      setError('Please select a bank.');
      return;
    }

    onMutate({
      accountNumber: data.accountNumber,
      accountName: data.accountName,
      bankCode: selectedBank,
      amount: data.amount,
      narration: data.narration,
    });
  };


  const validateAccount = async () => {
    setIsValidating(true);
    try {
      const response = await getBankValidation(selectedBank, accountNumber);
      console.log("Validation Response:", response);

      if (response.data?.account?.accountName) {
        setValue('accountName', response.data.account.accountName);
      } else {
        setError('Invalid account number or bank.');
      }
    } catch (error) {
      console.error("Validation Error:", error);
      setError('Invalid account number or bank.');
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <div>
      <div className="bg-white relative rounded-lg shadow p-6">
        {isValidating && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50">
            <div className="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}

        <h2 className="text-xl font-semibold mb-4">Bank Transfer</h2>

        {error && <div className="mb-4 p-3 bg-red-50 text-red-500 rounded">{error}</div>}
        {success && <div className="mb-4 p-3 bg-green-50 text-green-500 rounded">{success}</div>}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Bank</label>
            <select
              {...register('bank')}
              className="mt-1 block border-2 p-3 w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option value="">Select a bank</option>
              {getBanks?.banks?.map((bank) => (
                <option key={`${bank.bankCode}-${bank.bankName}`} value={bank.bankCode}>
                  {bank.bankName} ({bank.bankCode})
                </option>
              ))}
            </select>
            {errors.bank && <p className="mt-1 text-sm text-red-500">{errors.bank.message}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Account Number</label>
            <input
              type="text"
              {...register('accountNumber')}
              onKeyUp={() => {
                if (accountNumber?.length === 10 && selectedBank) {
                  validateAccount();
                }
              }}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
            {errors.accountNumber && <p className="mt-1 text-sm text-red-500">{errors.accountNumber.message}</p>}
          </div>


          <div>
            <label className="block text-sm font-medium text-gray-700">Account Name</label>
            <input
              type="text"
              {...register('accountName')}
              readOnly
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
            {errors.accountName && <p className="mt-1 text-sm text-red-500">{errors.accountName.message}</p>}
          </div>





          <div>
            <label className="block text-sm font-medium text-gray-700">Amount</label>
            <input
              type="number"
              step="0.01"
              {...register('amount', { valueAsNumber: true })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
            {errors.amount && <p className="mt-1 text-sm text-red-500">{errors.amount.message}</p>}
          </div>


          <div>
            <label className="block text-sm font-medium text-gray-700">Narration (Optional)</label>
            <input
              type="text"
              {...register('narration')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
          </div>


          <button
            type="submit"
            disabled={isLoading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {isLoading ? 'Processing...' : 'Transfer'}
          </button>
        </form>



      </div>

    </div>
  );
}
