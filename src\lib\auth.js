import { fetchApi } from './api';

export async function signIn({ email, password }) {
  console.log('signing in');
  console.log(email, password);
  const data = await fetchApi('/api/v1/auth/admin', {
    method: 'POST',
    body: JSON.stringify({ username:email, password }),
  });

  // Store the token in localStorage
  console.log(data);
  if (data.valid && data.entity.token) {
    localStorage.setItem('authToken', data.entity.token);
  }

  return data;
}

export async function signOut() {
  localStorage.removeItem('authToken');
     
}

export function getAuthToken() {
  return localStorage.getItem('authToken');
}

export async function getCurrentUser() {
  const token = getAuthToken();
  if (!token) return null;

  try {
    const data = await fetchApi('/api/v1/admin/manage/me', {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });
    return data;
  } catch (error) {
    // If token is invalid, clear it
    if (error.message === 'Unauthorized') {
      localStorage.removeItem('authToken');
    }
    return null;
  }
}