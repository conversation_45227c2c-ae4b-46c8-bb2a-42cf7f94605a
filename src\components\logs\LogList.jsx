import { useState, useEffect } from 'react';
import { useFetchLogs } from '../../query/logs';
import { useQueryClient } from "@tanstack/react-query";
import Modal from '../../shared/Modal'
import { deleteBatchLog, deleteLog } from '../../lib/logs';
import { toast } from 'react-toastify';

export function LogList() {
  const [filter, setFilter] = useState({
    severity: '',
    activity: '',
    page: 1,
    search: '',
    startDate: '',
    endDate: ''
  });

  const [selectedLogs, setSelectedLogs] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const queryClient = useQueryClient();

  const [showModal, setShowModal] = useState(false);
  const [logToDelete, setLogToDelete] = useState(null);

  const { data: logs, isPending, isError } = useFetchLogs(filter);

  useEffect(() => {
    setFilter(prev => ({ ...prev, page: 1 }));
  }, [filter.severity, filter.activity, filter.search, filter.startDate, filter.endDate]);

  const handleCheckboxChange = (id) => {
    setSelectedLogs((prevSelected) =>
      prevSelected.includes(id)
        ? prevSelected.filter((logId) => logId !== id)
        : [...prevSelected, id]
    );
    setSelectAll(false);
  };

  const handleSelectAllChange = () => {
    setSelectAll(!selectAll);
    if (!selectAll) {
      setSelectedLogs(logs?.data?.map(log => log._id));
    } else {
      setSelectedLogs([]);
    }
  };

  const handleSingleDeleteClick = (logId) => {
    setLogToDelete(logId);
    setShowModal(true);
  };

  const handleDelete = async () => {
    console.log('Selected Logs:', selectedLogs);


    if (selectedLogs.length > 1) {
      const requestBody = { ids: selectedLogs };
      console.log('Batch Deletion Request Body:', requestBody);
      const response = await deleteBatchLog(requestBody);
      if (response) {
        toast.success("Logs deleted successfully");
        setShowModal(false)

        setSelectedLogs([]);
        queryClient.invalidateQueries(["logs"]);

      } else {
        toast.error("Error deleting batch logs");
      }
    }

    else {
      console.log('Single Deletion Request Body:', selectedLogs);
      const response = await deleteLog(selectedLogs);
      if (response) {
        toast.success("Log deleted successfully");
        setShowModal(false);
        setSelectedLogs([]);
      } else {
        toast.error("Error deleting single log");
      }
    }
  };


  const handlePageChange = (direction) => {
    setFilter(prev => ({ ...prev, page: prev.page + direction }));
  };

  return (
    <div className="space-y-6">

      <div className="flex flex-wrap items-center gap-4 mb-4">
        <select
          value={filter.severity}
          onChange={(e) =>
            setFilter((f) => ({ ...f, severity: e.target.value }))
          }
          className="px-4 py-2 border rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
        >
          <option value="">All Severities</option>
          <option value="info">Info</option>
          <option value="warning">Warning</option>
          <option value="error">Error</option>
        </select>

        <input
          type="text"
          placeholder="Search"
          value={filter.search}
          onChange={(e) =>
            setFilter((f) => ({ ...f, search: e.target.value }))
          }
          className="flex-grow px-4 py-2 border rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
        />

        <div className="flex items-center space-x-4">
          <input
            type="date"
            value={filter.startDate}
            onChange={(e) =>
              setFilter((f) => ({ ...f, startDate: e.target.value }))
            }
            className="px-4 py-2 border rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
          <input
            type="date"
            value={filter.endDate}
            onChange={(e) =>
              setFilter((f) => ({ ...f, endDate: e.target.value }))
            }
            className="px-4 py-2 border rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>

        <button
          onClick={() => setShowModal(true)}
          disabled={selectedLogs.length === 0}
          className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:bg-gray-400"
        >
          Delete Selected
        </button>
      </div>


      <div className="overflow-x-auto shadow rounded-lg border border-gray-200">
        <table className="min-w-full divide-y divide-gray-200 table-auto">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                <input
                  type="checkbox"
                  checked={selectAll}
                  onChange={handleSelectAllChange}
                  className="form-checkbox h-5 w-5 text-indigo-600"
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Timestamp</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Activity</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Severity</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Message</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {logs?.data?.length > 0 ? (
              logs.data.map((log) => (
                <tr key={log._id}>
                  <td className="px-4 py-4 text-center">
                    <input
                      type="checkbox"
                      checked={selectedLogs.includes(log._id)}
                      onChange={() => handleCheckboxChange(log._id)}
                      className="form-checkbox h-5 w-5 text-indigo-600"
                    />
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">{new Date(log.created_at).toLocaleString()}</td>
                  <td className="px-6 py-4 text-sm">{log.activity}</td>
                  <td className="px-6 py-4">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${log.severity === 'error' ? 'bg-red-100 text-red-800' : log.severity === 'warning' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>{log.severity}</span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">{log.message}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="5" className="px-6 py-4 text-center text-gray-500">
                  Logs not found
                </td>
              </tr>
            )}
          </tbody>

        </table>
      </div>




      <div className="flex justify-between items-center mt-4">
        <button
          onClick={() => handlePageChange(-1)}
          disabled={filter.page === 1}
          className="px-4 py-2 border rounded-md text-gray-700 disabled:opacity-50"
        >
          Previous
        </button>
        <span className="text-sm text-gray-500">Page {filter.page}</span>
        <button
          onClick={() => handlePageChange(1)}
          disabled={filter.page === logs?.totalPages || filter.page > logs?.totalPages}
          className="px-4 py-2 border rounded-md text-gray-700 disabled:opacity-50"
        >
          Next
        </button>
      </div>
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onConfirm={handleDelete}
        title="Are you sure you want to delete?"
        message={selectedLogs.length > 1
          ? `You are about to delete ${selectedLogs.length} logs.`
          : "You are about to delete this log."}
      />


    </div>
  );
}
